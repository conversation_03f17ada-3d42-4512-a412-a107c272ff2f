2025-07-26 08:52:47,393 - stocks.trend - INFO - 📝 Logging configured: results/20250726/logs/ibkr_trading.log
2025-07-26 08:52:47,393 - __main__ - INFO - 🚀 Starting complete trading analysis...
2025-07-26 08:52:47,393 - stocks.trend - INFO - 🚀 Starting daily trading analysis workflow...
2025-07-26 08:52:47,393 - stocks.trend - INFO - 🔧 System configuration: enable_options=True
2025-07-26 08:52:47,393 - stocks.trend - INFO - ============================================================
2025-07-26 08:52:47,393 - stocks.trend - INFO - Skipping stock universe update (use --update-universe to enable)
2025-07-26 08:52:47,393 - stocks.trend - INFO - Step 1: Downloading data for all stocks...
2025-07-26 08:52:47,393 - stocks.trend - INFO - Found 457 symbols to analyze
2025-07-26 08:52:47,393 - stocks.trend - INFO - Downloading data for ALL 457 symbols to maximize correlation opportunities
2025-07-26 08:52:47,393 - stocks.trend - INFO - Downloading data from 2024-07-26 to 2025-07-26
2025-07-26 08:52:47,393 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 185...
2025-07-26 08:52:47,393 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 185...
2025-07-26 08:52:47,394 - ib_insync.client - INFO - Connected
2025-07-26 08:52:47,650 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 08:52:47,678 - ib_insync.client - INFO - API connection ready
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 08:52:47,712 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 08:52:47,713 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 08:52:48,095 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 08:52:48,096 - IBKRClient - INFO - Connected to IBKR at 127.0.0.1:7497
2025-07-26 08:52:48,157 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 08:52:48,157 - core.market_data - INFO - Connected to IBKR for sequential downloading (optimized for data farm stability)
2025-07-26 08:52:48,157 - core.market_data - INFO - 🚀 Starting optimized concurrent download
2025-07-26 08:52:48,157 - core.market_data - INFO - 📊 Total stocks: 457 (cached: 0, downloading: 457)
2025-07-26 08:52:48,157 - core.market_data - INFO - ⚡ Adaptive concurrent limit: 1
2025-07-26 08:52:48,157 - core.market_data - INFO - 📅 Date range: 2024-07-26 to 2025-07-26
2025-07-26 08:52:48,158 - core.market_data - INFO - 📂 Progress loaded: 456 completed, 1 failed, 0 invalid
2025-07-26 08:52:48,158 - core.market_data - INFO - 🔄 Resuming download from symbol 457/457
2025-07-26 08:52:48,158 - core.market_data - INFO - 📊 Previous progress: 456 completed, 1 failed
2025-07-26 08:52:48,158 - core.market_data - INFO - ✅ All data available from previous downloads, loading from incremental data...
2025-07-26 08:52:48,170 - core.market_data - INFO - 📊 Loading stock classifications from data/stock_info/stock_info.csv
2025-07-26 08:52:48,187 - core.market_data - INFO - ✅ Updated classifications for 457 stocks
2025-07-26 08:52:48,187 - core.market_data - INFO - 📦 Loaded 401 stocks from incremental data
2025-07-26 08:52:48,187 - stocks.trend - INFO - ✅ IBKR connection transferred to main client - keeping alive for trading
2025-07-26 08:52:48,187 - stocks.trend - INFO - Successfully downloaded data for 401 stocks
2025-07-26 08:52:48,187 - stocks.trend - INFO - Data period: 2024-02-09 00:00:00 to 2025-07-25 00:00:00
2025-07-26 08:52:48,188 - stocks.trend - INFO - Step 2: Running ML model analysis...
2025-07-26 08:52:48,188 - stocks.trend - WARNING - Price data is a list (inhomogeneous shapes detected)
2025-07-26 08:52:48,188 - stocks.trend - INFO - Number of stocks: 401
2025-07-26 08:52:48,188 - stocks.trend - INFO - Price array lengths: min=246, max=365
2025-07-26 08:52:48,192 - stocks.trend - INFO - ✅ Selected 401 stocks with sufficient data (60 days each)
2025-07-26 08:52:48,192 - stocks.trend - INFO - ❌ Rejected 0 stocks with insufficient data (< 60 days)
2025-07-26 08:52:48,192 - stocks.trend - INFO - 📊 Data quality: Using only stocks with robust historical data for scientific analysis
2025-07-26 08:52:48,192 - stocks.trend - INFO - Initial data shape: (401, 60)
2025-07-26 08:52:48,192 - stocks.trend - INFO - Price data range: min=0.1513, max=937.7600
2025-07-26 08:52:48,192 - stocks.trend - INFO - NaN count: 0, Zero count: 0
2025-07-26 08:52:48,192 - stocks.trend - INFO - Performing advanced data cleaning...
2025-07-26 08:52:48,197 - stocks.trend - INFO - Good data quality (0.0%), using relaxed validation for more stocks
2025-07-26 08:52:48,202 - stocks.trend - INFO - Data cleaning results:
2025-07-26 08:52:48,202 - stocks.trend - INFO -   Original stocks: 401
2025-07-26 08:52:48,202 - stocks.trend - INFO -   Valid stocks after cleaning: 401
2025-07-26 08:52:48,202 - stocks.trend - INFO -   Rejection rate: 0.0%
2025-07-26 08:52:48,202 - stocks.trend - INFO - Filtered to 401 stocks with valid price data (from 401)
2025-07-26 08:52:48,204 - stocks.trend - INFO - Final dataset: 401 stocks ready for analysis
2025-07-26 08:52:48,204 - stocks.trend - INFO - Training model with 401 stocks, 60 time periods
2025-07-26 08:52:48,206 - stocks.trend - INFO - Data consistency check:
2025-07-26 08:52:48,206 - stocks.trend - INFO -   Stocks in price data: 401
2025-07-26 08:52:48,206 - stocks.trend - INFO -   Stocks in hierarchical info: 401
2025-07-26 08:52:48,206 - stocks.trend - INFO -   Sectors dict length: 401
2025-07-26 08:52:48,206 - stocks.trend - INFO -   Industries dict length: 401
2025-07-26 08:52:48,206 - stocks.trend - INFO - Training MSIS-MCS model...
2025-07-26 08:52:48,206 - stocks.trend - INFO - Final training data: logp shape=(401, 60), info stocks=401
2025-07-26 08:52:54,511 - core.stats - INFO - Estimating log price statistics: phi shape=(401, 16), psi shape=(401, 1), tt_pred shape=(16, 6)
2025-07-26 08:52:54,519 - core.stats - INFO - Estimated log price predictions: shape=(401, 6), std shape=(401, 6)
2025-07-26 08:52:54,520 - core.stats - INFO - Estimated price predictions: shape=(401, 6), std shape=(401, 6)
2025-07-26 08:52:54,521 - stocks.trend - INFO - 📊 Trend Score Distribution Analysis:
2025-07-26 08:52:54,521 - stocks.trend - INFO -   Min: -7.445, Max: 3.314
2025-07-26 08:52:54,521 - stocks.trend - INFO -   Mean: -2.996, Std: 1.502
2025-07-26 08:52:54,521 - stocks.trend - INFO -    1th percentile: -5.617
2025-07-26 08:52:54,522 - stocks.trend - INFO -    5th percentile: -4.611
2025-07-26 08:52:54,522 - stocks.trend - INFO -   10th percentile: -4.299
2025-07-26 08:52:54,522 - stocks.trend - INFO -   25th percentile: -3.931
2025-07-26 08:52:54,522 - stocks.trend - INFO -   50th percentile: -3.422
2025-07-26 08:52:54,522 - stocks.trend - INFO -   75th percentile: -2.533
2025-07-26 08:52:54,522 - stocks.trend - INFO -   90th percentile: -0.957
2025-07-26 08:52:54,522 - stocks.trend - INFO -   95th percentile: -0.177
2025-07-26 08:52:54,522 - stocks.trend - INFO -   99th percentile:  2.172
2025-07-26 08:52:54,522 - stocks.trend - INFO - 📈 Stock Count by Range:
2025-07-26 08:52:54,522 - stocks.trend - INFO -   < -5.0      :   10 stocks (  2.5%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   -5.0 to -4.0:   74 stocks ( 18.5%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   -4.0 to -3.0:  173 stocks ( 43.1%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   -3.0 to -2.5:   46 stocks ( 11.5%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   -2.5 to -2.0:   15 stocks (  3.7%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   -2.0 to -1.0:   38 stocks (  9.5%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   -1.0 to 0.0 :   28 stocks (  7.0%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   0.0 to 1.0  :    7 stocks (  1.7%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   1.0 to 2.0  :    4 stocks (  1.0%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   2.0 to 2.5  :    3 stocks (  0.7%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   2.5 to 3.0  :    1 stocks (  0.2%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   3.0 to 4.0  :    2 stocks (  0.5%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   4.0 to 5.0  :    0 stocks (  0.0%)
2025-07-26 08:52:54,522 - stocks.trend - INFO -   > 5.0       :    0 stocks (  0.0%)
2025-07-26 08:52:54,523 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,523 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,523 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,523 - stocks.trend - INFO - Using 'high' extreme level with bounds: {'EXTREME BELOW TREND': np.float64(-10.616776622230024), 'HIGHLY BELOW TREND': np.float64(-5.616776622230023), 'BELOW TREND': np.float64(-4.611292416416023), 'ALONG TREND': np.float64(-0.9571745485551683), 'ABOVE TREND': np.float64(-0.176905626549547), 'HIGHLY ABOVE TREND': np.float64(2.1719558589793464), 'EXTREME ABOVE TREND': inf}
2025-07-26 08:52:54,523 - stocks.trend - INFO - 🔍 Generating ML correlation matches for options trading...
2025-07-26 08:52:54,535 - core.stats - INFO - Estimated matches for 401 stocks
2025-07-26 08:52:54,535 - stocks.trend - INFO - ✅ Generated 401 ML correlation matches
2025-07-26 08:52:54,541 - stocks.trend - INFO - Model analysis completed successfully
2025-07-26 08:52:54,541 - stocks.trend - INFO - Stocks above trend: 40
2025-07-26 08:52:54,542 - stocks.trend - INFO - Stocks below trend: 20
2025-07-26 08:52:54,542 - stocks.trend - INFO - Stocks along trend: 341
2025-07-26 08:52:54,543 - stocks.trend - INFO - Step 3: Performing correlation analysis...
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 0 stocks EXTREME BELOW trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 4 stocks HIGHLY BELOW trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 16 stocks BELOW trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 341 stocks ALONG trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 20 stocks ABOVE trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 16 stocks HIGHLY ABOVE trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Found 4 stocks EXTREME ABOVE trend
2025-07-26 08:52:54,543 - stocks.trend - INFO - Identified 0 correlation opportunities
2025-07-26 08:52:54,544 - stocks.trend - INFO - Step 4: Executing trades based on correlation analysis...
2025-07-26 08:52:54,544 - stocks.trend - INFO - ✅ Using existing IBKR connection for trading
2025-07-26 08:52:54,544 - stocks.trend - INFO - Betty bot prefers strong stocks - prioritizing EXTREME ABOVE TREND candidates
2025-07-26 08:52:54,545 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,545 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,545 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,545 - stocks.trend - INFO - 🔥 ULTRA RARE: ONMD (score: 3.31)
2025-07-26 08:52:54,546 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,546 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,546 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,546 - stocks.trend - INFO - 🔥 ULTRA RARE: OPI (score: 3.05)
2025-07-26 08:52:54,547 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,547 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,547 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,547 - stocks.trend - INFO - 🔥 ULTRA RARE: ZCMD (score: 3.00)
2025-07-26 08:52:54,547 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,547 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,547 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,547 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,547 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,547 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,548 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,548 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,548 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,548 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,548 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,548 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,548 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,548 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,548 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,549 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,549 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,549 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,549 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 08:52:54,549 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 08:52:54,549 - stocks.trend - INFO -    90th percentile: -1.0, 95th: -0.2, 99th: 2.2
2025-07-26 08:52:54,549 - stocks.trend - INFO - Executing trades for 10 stocks...
2025-07-26 08:52:54,550 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 185...
2025-07-26 08:52:54,550 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 185...
2025-07-26 08:52:54,558 - ib_insync.client - INFO - Connected
2025-07-26 08:52:54,578 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 08:52:54,618 - ib_insync.wrapper - ERROR - Error 326, reqId -1: \u5ba2\u6237\u53f7\u7801\u5df2\u88ab\u4f7f\u7528\uff0c\u65e0\u6cd5\u8fde\u63a5\u3002\u8bf7\u7528\u672a\u88ab\u4f7f\u7528\u7684\u5ba2\u6237\u53f7\u7801\u91cd\u8bd5\u3002
2025-07-26 08:52:55,130 - ib_insync.client - ERROR - Peer closed connection. clientId 185 already in use?
2025-07-26 08:52:58,562 - ib_insync.client - INFO - Disconnecting
2025-07-26 08:52:58,563 - ib_insync.client - ERROR - API connection failed: TimeoutError()
2025-07-26 08:52:58,564 - IBKRClient - ERROR - Failed to connect to IBKR: 
2025-07-26 08:52:58,564 - stocks.trend - WARNING - ❌ Failed to connect bot to IBKR - trades will remain in simulation mode
2025-07-26 08:52:58,564 - stocks.trend - INFO - Trading completed:
2025-07-26 08:52:58,564 - stocks.trend - INFO - Portfolio value: $100000.00
2025-07-26 08:52:58,564 - stocks.trend - INFO - Cash: $76668.87
2025-07-26 08:52:58,564 - stocks.trend - INFO - Invested: $23331.13
2025-07-26 08:52:58,564 - stocks.trend - INFO - Positions: 7
2025-07-26 08:52:58,565 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 712 B sent in 9 messages, 30.2 kB received in 653 messages, session time 11.2 s.
2025-07-26 08:52:58,565 - ib_insync.client - INFO - Disconnecting
2025-07-26 08:52:58,565 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 08:52:58,565 - stocks.trend - INFO - 🔍 Checking options arbitrage: enable_options=True
2025-07-26 08:52:58,565 - stocks.trend - INFO - ✅ Options arbitrage is ENABLED - starting Step 5
2025-07-26 08:52:58,566 - stocks.trend - INFO - 🚀 Step 5: Analyzing options arbitrage opportunities...
2025-07-26 08:52:58,566 - stocks.trend - INFO - 📊 Available data: model_results=True, correlation_analysis=True
2025-07-26 08:52:58,566 - stocks.trend - INFO - 📈 Correlation analysis categories: ['extreme_below_trend', 'highly_below_trend', 'below_trend', 'along_trend', 'above_trend', 'highly_above_trend', 'extreme_above_trend', 'correlation_opportunities', 'sector_groups']
2025-07-26 08:52:58,566 - stocks.trend - INFO -    extreme_below_trend: 0 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    highly_below_trend: 4 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    below_trend: 16 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    along_trend: 341 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    above_trend: 20 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    highly_above_trend: 16 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    extreme_above_trend: 4 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO -    correlation_opportunities: 0 stocks
2025-07-26 08:52:58,566 - stocks.trend - INFO - 🔧 Importing options arbitrage system...
2025-07-26 08:52:59,432 - stocks.trend - INFO - ⚙️ Initializing options arbitrage system...
2025-07-26 08:52:59,432 - options.arbitrage - INFO - ✅ Using optimized options configuration
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    Min correlation: 0.01
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    Min divergence: 0.2
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    Max option days: 30
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    Max risk per trade: 1.2%
2025-07-26 08:52:59,432 - options.arbitrage - INFO - ✅ Options performance monitor initialized
2025-07-26 08:52:59,432 - stocks.trend - INFO - 💰 Options capital allocation: $30,000.00 (30% of $100,000.00)
2025-07-26 08:52:59,432 - stocks.trend - INFO - 🎯 Starting options arbitrage analysis...
2025-07-26 08:52:59,432 - options.arbitrage - INFO - 🎯 Starting Options Arbitrage Analysis...
2025-07-26 08:52:59,432 - options.arbitrage - INFO - ============================================================
2025-07-26 08:52:59,432 - options.arbitrage - INFO - 💰 Available capital: $30,000.00
2025-07-26 08:52:59,432 - options.arbitrage - INFO - 📊 Input data status:
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    - Correlation analysis: True
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    - Model results: True
2025-07-26 08:52:59,432 - options.arbitrage - INFO -    - Correlation categories: ['extreme_below_trend', 'highly_below_trend', 'below_trend', 'along_trend', 'above_trend', 'highly_above_trend', 'extreme_above_trend', 'correlation_opportunities', 'sector_groups']
2025-07-26 08:52:59,432 - options.arbitrage - INFO -      * extreme_below_trend: 0 stocks
2025-07-26 08:52:59,432 - options.arbitrage - INFO -      * highly_below_trend: 4 stocks
2025-07-26 08:52:59,432 - options.arbitrage - INFO -      * below_trend: 16 stocks
2025-07-26 08:52:59,432 - options.arbitrage - INFO -      * along_trend: 341 stocks
2025-07-26 08:52:59,432 - options.arbitrage - INFO -      * above_trend: 20 stocks
2025-07-26 08:52:59,432 - options.arbitrage - INFO -      * highly_above_trend: 16 stocks
2025-07-26 08:52:59,433 - options.arbitrage - INFO -      * extreme_above_trend: 4 stocks
2025-07-26 08:52:59,433 - options.arbitrage - INFO -      * correlation_opportunities: 0 stocks
2025-07-26 08:52:59,433 - options.arbitrage - INFO -    - Model results keys: ['tickers', 'current_prices', 'predicted_prices', 'trend_scores', 'ratings', 'sectors', 'industries', 'matches', 'price_data', 'model_params']
2025-07-26 08:52:59,433 - options.arbitrage - INFO -      * ML matches available: 401 stocks
2025-07-26 08:52:59,433 - options.arbitrage - INFO - 🔍 Step 1: Analyzing correlation opportunities...
2025-07-26 08:52:59,433 - options.arbitrage - INFO - 🔍 Analyzing correlation arbitrage opportunities (EXTREME + HIGHLY levels)...
2025-07-26 08:52:59,433 - options.arbitrage - INFO - 📊 Found target stocks: 0 EXTREME BELOW, 4 EXTREME ABOVE, 4 HIGHLY BELOW, 16 HIGHLY ABOVE
2025-07-26 08:52:59,433 - options.arbitrage - INFO - 🤖 ML-first analysis for EXTREME ABOVE TREND stock: ONMD (Software)
2025-07-26 08:52:59,433 - options.arbitrage - INFO - ✅ Found ML model data for ONMD, proceeding with ML-first analysis
2025-07-26 08:52:59,433 - options.arbitrage - INFO - 🔍 Checking ML model data for ONMD
2025-07-26 08:52:59,433 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:52:59,433 - options.arbitrage - INFO -   🎯 ML model found: FFAI (distance=0.206, correlation=0.829)
2025-07-26 08:52:59,435 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: FFAI
2025-07-26 08:52:59,435 - options.arbitrage - INFO -      ML: r=0.829 (ML-only validation)
2025-07-26 08:52:59,435 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for ONMD
2025-07-26 08:53:00,373 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_ONMD_20250726_085259.png
2025-07-26 08:53:00,374 - options.arbitrage - INFO - 🎯 ML-validated correlations for ONMD: 1 opportunities
2025-07-26 08:53:00,374 - options.arbitrage - INFO - 🤖 ML-first analysis for EXTREME ABOVE TREND stock: OPI (REITS)
2025-07-26 08:53:00,374 - options.arbitrage - INFO - ✅ Found ML model data for OPI, proceeding with ML-first analysis
2025-07-26 08:53:00,374 - options.arbitrage - INFO - 🔍 Checking ML model data for OPI
2025-07-26 08:53:00,374 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:00,375 - options.arbitrage - INFO -   🎯 ML model found: SWIN (distance=0.215, correlation=0.823)
2025-07-26 08:53:00,375 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: SWIN
2025-07-26 08:53:00,375 - options.arbitrage - INFO -      ML: r=0.823 (ML-only validation)
2025-07-26 08:53:00,376 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for OPI
2025-07-26 08:53:00,868 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_OPI_20250726_085300.png
2025-07-26 08:53:00,868 - options.arbitrage - INFO - 🎯 ML-validated correlations for OPI: 1 opportunities
2025-07-26 08:53:00,868 - options.arbitrage - INFO - 🤖 ML-first analysis for EXTREME ABOVE TREND stock: SCWO (Environmental Control)
2025-07-26 08:53:00,869 - options.arbitrage - INFO - ✅ Found ML model data for SCWO, proceeding with ML-first analysis
2025-07-26 08:53:00,869 - options.arbitrage - INFO - 🔍 Checking ML model data for SCWO
2025-07-26 08:53:00,869 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:00,869 - options.arbitrage - INFO -   🎯 ML model found: GPRO (distance=0.170, correlation=0.855)
2025-07-26 08:53:00,869 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: GPRO
2025-07-26 08:53:00,869 - options.arbitrage - INFO -      ML: r=0.855 (ML-only validation)
2025-07-26 08:53:00,869 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for SCWO
2025-07-26 08:53:01,382 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_SCWO_20250726_085300.png
2025-07-26 08:53:01,383 - options.arbitrage - INFO - 🎯 ML-validated correlations for SCWO: 1 opportunities
2025-07-26 08:53:01,383 - options.arbitrage - INFO - 🤖 ML-first analysis for EXTREME ABOVE TREND stock: ZCMD (Internet)
2025-07-26 08:53:01,383 - options.arbitrage - INFO - ✅ Found ML model data for ZCMD, proceeding with ML-first analysis
2025-07-26 08:53:01,383 - options.arbitrage - INFO - 🔍 Checking ML model data for ZCMD
2025-07-26 08:53:01,383 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:01,383 - options.arbitrage - INFO -   🎯 ML model found: CNVS (distance=0.091, correlation=0.917)
2025-07-26 08:53:01,383 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: CNVS
2025-07-26 08:53:01,383 - options.arbitrage - INFO -      ML: r=0.917 (ML-only validation)
2025-07-26 08:53:01,383 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for ZCMD
2025-07-26 08:53:01,903 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_ZCMD_20250726_085301.png
2025-07-26 08:53:01,905 - options.arbitrage - INFO - 🎯 ML-validated correlations for ZCMD: 1 opportunities
2025-07-26 08:53:01,905 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY BELOW TREND stock: BLDP (Energy-Alternate Sources)
2025-07-26 08:53:01,905 - options.arbitrage - INFO - ✅ Found ML model data for BLDP, proceeding with ML-first analysis
2025-07-26 08:53:01,905 - options.arbitrage - INFO - 🔍 Checking ML model data for BLDP
2025-07-26 08:53:01,905 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:01,905 - options.arbitrage - INFO -   🎯 ML model found: RCON (distance=0.219, correlation=0.820)
2025-07-26 08:53:01,906 - options.arbitrage - INFO -   ✅ Dual validation passed: RCON
2025-07-26 08:53:01,906 - options.arbitrage - INFO -      ML: r=0.820, Traditional: r=0.199
2025-07-26 08:53:01,906 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for BLDP
2025-07-26 08:53:02,373 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_BLDP_20250726_085301.png
2025-07-26 08:53:02,373 - options.arbitrage - INFO - 🎯 ML-validated correlations for BLDP: 1 opportunities
2025-07-26 08:53:02,373 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY BELOW TREND stock: HSHP (Transportation)
2025-07-26 08:53:02,373 - options.arbitrage - INFO - ✅ Found ML model data for HSHP, proceeding with ML-first analysis
2025-07-26 08:53:02,373 - options.arbitrage - INFO - 🔍 Checking ML model data for HSHP
2025-07-26 08:53:02,373 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:02,373 - options.arbitrage - INFO -   🎯 ML model found: RNA (distance=0.058, correlation=0.945)
2025-07-26 08:53:02,373 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: RNA
2025-07-26 08:53:02,373 - options.arbitrage - INFO -      ML: r=0.945 (ML-only validation)
2025-07-26 08:53:02,373 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for HSHP
2025-07-26 08:53:02,851 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_HSHP_20250726_085302.png
2025-07-26 08:53:02,851 - options.arbitrage - INFO - 🎯 ML-validated correlations for HSHP: 1 opportunities
2025-07-26 08:53:02,851 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY BELOW TREND stock: RCON (Oil&Gas Services)
2025-07-26 08:53:02,851 - options.arbitrage - INFO - ✅ Found ML model data for RCON, proceeding with ML-first analysis
2025-07-26 08:53:02,851 - options.arbitrage - INFO - 🔍 Checking ML model data for RCON
2025-07-26 08:53:02,852 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:02,852 - options.arbitrage - INFO -   🎯 ML model found: USEG (distance=0.162, correlation=0.861)
2025-07-26 08:53:02,852 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: USEG
2025-07-26 08:53:02,852 - options.arbitrage - INFO -      ML: r=0.861 (ML-only validation)
2025-07-26 08:53:02,852 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for RCON
2025-07-26 08:53:03,315 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_RCON_20250726_085302.png
2025-07-26 08:53:03,315 - options.arbitrage - INFO - 🎯 ML-validated correlations for RCON: 1 opportunities
2025-07-26 08:53:03,315 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY BELOW TREND stock: RKLB (Aerospace/Defense)
2025-07-26 08:53:03,315 - options.arbitrage - INFO - ✅ Found ML model data for RKLB, proceeding with ML-first analysis
2025-07-26 08:53:03,315 - options.arbitrage - INFO - 🔍 Checking ML model data for RKLB
2025-07-26 08:53:03,315 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:03,315 - options.arbitrage - INFO -   🎯 ML model found: ROAD (distance=0.014, correlation=0.986)
2025-07-26 08:53:03,316 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: ROAD
2025-07-26 08:53:03,316 - options.arbitrage - INFO -      ML: r=0.986 (ML-only validation)
2025-07-26 08:53:03,316 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for RKLB
2025-07-26 08:53:03,822 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_RKLB_20250726_085303.png
2025-07-26 08:53:03,823 - options.arbitrage - INFO - 🎯 ML-validated correlations for RKLB: 1 opportunities
2025-07-26 08:53:03,823 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: CCTG (Investment Companies)
2025-07-26 08:53:03,823 - options.arbitrage - INFO - ✅ Found ML model data for CCTG, proceeding with ML-first analysis
2025-07-26 08:53:03,823 - options.arbitrage - INFO - 🔍 Checking ML model data for CCTG
2025-07-26 08:53:03,823 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:03,823 - options.arbitrage - INFO -   🎯 ML model found: ATUS (distance=0.158, correlation=0.864)
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   ❌ ML divergence too low: 0.15 < 0.2
2025-07-26 08:53:03,824 - options.arbitrage - INFO - ❌ No valid ML correlations found for CCTG - SKIPPING
2025-07-26 08:53:03,824 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: CLPS (Computers)
2025-07-26 08:53:03,824 - options.arbitrage - INFO - ✅ Found ML model data for CLPS, proceeding with ML-first analysis
2025-07-26 08:53:03,824 - options.arbitrage - INFO - 🔍 Checking ML model data for CLPS
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   🎯 ML model found: LITB (distance=0.079, correlation=0.927)
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   ❌ ML divergence too low: 0.01 < 0.2
2025-07-26 08:53:03,824 - options.arbitrage - INFO - ❌ No valid ML correlations found for CLPS - SKIPPING
2025-07-26 08:53:03,824 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: CMND (Biotechnology)
2025-07-26 08:53:03,824 - options.arbitrage - INFO - ✅ Found ML model data for CMND, proceeding with ML-first analysis
2025-07-26 08:53:03,824 - options.arbitrage - INFO - 🔍 Checking ML model data for CMND
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   🎯 ML model found: MNY (distance=0.137, correlation=0.879)
2025-07-26 08:53:03,824 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: MNY
2025-07-26 08:53:03,824 - options.arbitrage - INFO -      ML: r=0.879 (ML-only validation)
2025-07-26 08:53:03,825 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for CMND
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_CMND_20250726_085303.png
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 🎯 ML-validated correlations for CMND: 1 opportunities
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: CTSO (Healthcare-Products)
2025-07-26 08:53:04,476 - options.arbitrage - INFO - ✅ Found ML model data for CTSO, proceeding with ML-first analysis
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 🔍 Checking ML model data for CTSO
2025-07-26 08:53:04,476 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:04,476 - options.arbitrage - INFO -   🎯 ML model found: QNCX (distance=0.039, correlation=0.963)
2025-07-26 08:53:04,476 - options.arbitrage - INFO -   ❌ ML divergence too low: 0.17 < 0.2
2025-07-26 08:53:04,476 - options.arbitrage - INFO - ❌ No valid ML correlations found for CTSO - SKIPPING
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: DHAI (Healthcare-Products)
2025-07-26 08:53:04,476 - options.arbitrage - INFO - ✅ Found ML model data for DHAI, proceeding with ML-first analysis
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 🔍 Checking ML model data for DHAI
2025-07-26 08:53:04,476 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:04,476 - options.arbitrage - INFO -   🎯 ML model found: SCWO (distance=0.270, correlation=0.787)
2025-07-26 08:53:04,476 - options.arbitrage - INFO -   ❌ ML divergence too low: 0.11 < 0.2
2025-07-26 08:53:04,476 - options.arbitrage - INFO - ❌ No valid ML correlations found for DHAI - SKIPPING
2025-07-26 08:53:04,476 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: FIEE (Telecommunications)
2025-07-26 08:53:04,476 - options.arbitrage - INFO - ✅ Found ML model data for FIEE, proceeding with ML-first analysis
2025-07-26 08:53:04,477 - options.arbitrage - INFO - 🔍 Checking ML model data for FIEE
2025-07-26 08:53:04,477 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:04,477 - options.arbitrage - INFO -   🎯 ML model found: SGMA (distance=0.114, correlation=0.898)
2025-07-26 08:53:04,477 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: SGMA
2025-07-26 08:53:04,477 - options.arbitrage - INFO -      ML: r=0.898 (ML-only validation)
2025-07-26 08:53:04,477 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for FIEE
2025-07-26 08:53:04,931 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_FIEE_20250726_085304.png
2025-07-26 08:53:04,932 - options.arbitrage - INFO - 🎯 ML-validated correlations for FIEE: 1 opportunities
2025-07-26 08:53:04,932 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: LITB (Internet)
2025-07-26 08:53:04,932 - options.arbitrage - INFO - ✅ Found ML model data for LITB, proceeding with ML-first analysis
2025-07-26 08:53:04,932 - options.arbitrage - INFO - 🔍 Checking ML model data for LITB
2025-07-26 08:53:04,932 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:04,932 - options.arbitrage - INFO -   🎯 ML model found: MNY (distance=0.051, correlation=0.952)
2025-07-26 08:53:04,932 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: MNY
2025-07-26 08:53:04,932 - options.arbitrage - INFO -      ML: r=0.952 (ML-only validation)
2025-07-26 08:53:04,932 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for LITB
2025-07-26 08:53:05,402 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_LITB_20250726_085304.png
2025-07-26 08:53:05,403 - options.arbitrage - INFO - 🎯 ML-validated correlations for LITB: 1 opportunities
2025-07-26 08:53:05,403 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: MNDR (Internet)
2025-07-26 08:53:05,403 - options.arbitrage - INFO - ✅ Found ML model data for MNDR, proceeding with ML-first analysis
2025-07-26 08:53:05,403 - options.arbitrage - INFO - 🔍 Checking ML model data for MNDR
2025-07-26 08:53:05,403 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:05,403 - options.arbitrage - INFO -   🎯 ML model found: ELAB (distance=0.136, correlation=0.880)
2025-07-26 08:53:05,403 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: ELAB
2025-07-26 08:53:05,403 - options.arbitrage - INFO -      ML: r=0.880 (ML-only validation)
2025-07-26 08:53:05,403 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for MNDR
2025-07-26 08:53:05,889 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_MNDR_20250726_085305.png
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🎯 ML-validated correlations for MNDR: 1 opportunities
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: OLPX (Cosmetics/Personal Care)
2025-07-26 08:53:05,890 - options.arbitrage - INFO - ✅ Found ML model data for OLPX, proceeding with ML-first analysis
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🔍 Checking ML model data for OLPX
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   🎯 ML model found: QNCX (distance=0.086, correlation=0.921)
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   ❌ ML divergence too low: 0.13 < 0.2
2025-07-26 08:53:05,890 - options.arbitrage - INFO - ❌ No valid ML correlations found for OLPX - SKIPPING
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: PETZ (Food)
2025-07-26 08:53:05,890 - options.arbitrage - INFO - ✅ Found ML model data for PETZ, proceeding with ML-first analysis
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🔍 Checking ML model data for PETZ
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   🎯 ML model found: PLRX (distance=0.136, correlation=0.880)
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   ❌ ML divergence too low: 0.18 < 0.2
2025-07-26 08:53:05,890 - options.arbitrage - INFO - ❌ No valid ML correlations found for PETZ - SKIPPING
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: PMEC (Commercial Services)
2025-07-26 08:53:05,890 - options.arbitrage - INFO - ✅ Found ML model data for PMEC, proceeding with ML-first analysis
2025-07-26 08:53:05,890 - options.arbitrage - INFO - 🔍 Checking ML model data for PMEC
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:05,890 - options.arbitrage - INFO -   🎯 ML model found: TRIB (distance=0.105, correlation=0.905)
2025-07-26 08:53:05,891 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: TRIB
2025-07-26 08:53:05,891 - options.arbitrage - INFO -      ML: r=0.905 (ML-only validation)
2025-07-26 08:53:05,891 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for PMEC
2025-07-26 08:53:06,360 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_PMEC_20250726_085305.png
2025-07-26 08:53:06,360 - options.arbitrage - INFO - 🎯 ML-validated correlations for PMEC: 1 opportunities
2025-07-26 08:53:06,360 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: PSTV (Biotechnology)
2025-07-26 08:53:06,360 - options.arbitrage - INFO - ✅ Found ML model data for PSTV, proceeding with ML-first analysis
2025-07-26 08:53:06,360 - options.arbitrage - INFO - 🔍 Checking ML model data for PSTV
2025-07-26 08:53:06,360 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:06,360 - options.arbitrage - INFO -   🎯 ML model found: TRIB (distance=0.160, correlation=0.862)
2025-07-26 08:53:06,361 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: TRIB
2025-07-26 08:53:06,361 - options.arbitrage - INFO -      ML: r=0.862 (ML-only validation)
2025-07-26 08:53:06,361 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for PSTV
2025-07-26 08:53:06,832 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_PSTV_20250726_085306.png
2025-07-26 08:53:06,832 - options.arbitrage - INFO - 🎯 ML-validated correlations for PSTV: 1 opportunities
2025-07-26 08:53:06,832 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: SWAG (Advertising)
2025-07-26 08:53:06,832 - options.arbitrage - INFO - ✅ Found ML model data for SWAG, proceeding with ML-first analysis
2025-07-26 08:53:06,832 - options.arbitrage - INFO - 🔍 Checking ML model data for SWAG
2025-07-26 08:53:06,832 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:06,832 - options.arbitrage - INFO -   🎯 ML model found: ZCMD (distance=0.111, correlation=0.900)
2025-07-26 08:53:06,833 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: ZCMD
2025-07-26 08:53:06,833 - options.arbitrage - INFO -      ML: r=0.900 (ML-only validation)
2025-07-26 08:53:06,833 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for SWAG
2025-07-26 08:53:07,290 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_SWAG_20250726_085306.png
2025-07-26 08:53:07,290 - options.arbitrage - INFO - 🎯 ML-validated correlations for SWAG: 1 opportunities
2025-07-26 08:53:07,290 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: TRIB (Healthcare-Products)
2025-07-26 08:53:07,290 - options.arbitrage - INFO - ✅ Found ML model data for TRIB, proceeding with ML-first analysis
2025-07-26 08:53:07,290 - options.arbitrage - INFO - 🔍 Checking ML model data for TRIB
2025-07-26 08:53:07,290 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:07,291 - options.arbitrage - INFO -   🎯 ML model found: PMEC (distance=0.105, correlation=0.905)
2025-07-26 08:53:07,291 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: PMEC
2025-07-26 08:53:07,291 - options.arbitrage - INFO -      ML: r=0.905 (ML-only validation)
2025-07-26 08:53:07,291 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for TRIB
2025-07-26 08:53:07,758 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_TRIB_20250726_085307.png
2025-07-26 08:53:07,759 - options.arbitrage - INFO - 🎯 ML-validated correlations for TRIB: 1 opportunities
2025-07-26 08:53:07,759 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: ZBAI (Diversified Finan Serv)
2025-07-26 08:53:07,759 - options.arbitrage - INFO - ✅ Found ML model data for ZBAI, proceeding with ML-first analysis
2025-07-26 08:53:07,759 - options.arbitrage - INFO - 🔍 Checking ML model data for ZBAI
2025-07-26 08:53:07,759 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:07,759 - options.arbitrage - INFO -   🎯 ML model found: FTFT (distance=0.094, correlation=0.914)
2025-07-26 08:53:07,759 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: FTFT
2025-07-26 08:53:07,759 - options.arbitrage - INFO -      ML: r=0.914 (ML-only validation)
2025-07-26 08:53:07,759 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for ZBAI
2025-07-26 08:53:08,224 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_ZBAI_20250726_085307.png
2025-07-26 08:53:08,224 - options.arbitrage - INFO - 🎯 ML-validated correlations for ZBAI: 1 opportunities
2025-07-26 08:53:08,224 - options.arbitrage - INFO - 🤖 ML-first analysis for HIGHLY ABOVE TREND stock: ZTEK (Miscellaneous Manufactur)
2025-07-26 08:53:08,224 - options.arbitrage - INFO - ✅ Found ML model data for ZTEK, proceeding with ML-first analysis
2025-07-26 08:53:08,225 - options.arbitrage - INFO - 🔍 Checking ML model data for ZTEK
2025-07-26 08:53:08,225 - options.arbitrage - INFO -   Available ML matches: ['AA', 'AAPL', 'ACA', 'ACCO', 'ACGLO', 'ACHV', 'ACN', 'ACNT', 'ACTG', 'AGCO', 'AHH', 'AIFF', 'AIOT', 'AIT', 'AIV', 'ALIT', 'ALLY', 'ALM', 'ALVO', 'AMBR', 'AMCR', 'ANET', 'AR', 'ARCC', 'ARGD', 'ASH', 'ASPI', 'ATMU', 'ATNF', 'ATUS', 'AURA', 'AVXL', 'AXTI', 'B', 'BABA', 'BASE', 'BBLG', 'BBN', 'BELFA', 'BG', 'BHFAM', 'BIOX', 'BKKT', 'BKR', 'BLDP', 'BLRX', 'BMRA', 'BNJ', 'BRZE', 'BTBD', 'BTCM', 'BTCS', 'BTI', 'BTO', 'BUD', 'BWAY', 'BZ', 'CABO', 'CALM', 'CAPR', 'CARS', 'CATO', 'CBL', 'CCB', 'CCLDO', 'CCTG', 'CDW', 'CELZ', 'CEVA', 'CGO', 'CHCT', 'CHKP', 'CIGI', 'CLPS', 'CLSK', 'CMND', 'CNH', 'CNVS', 'COEP', 'CPB', 'CPRX', 'CPT', 'CRNC', 'CRVO', 'CSCI', 'CTBB', 'CTSO', 'CVEO', 'DB', 'DBRG', 'DCI', 'DDI', 'DEO', 'DHAI', 'DHI', 'DIN', 'DLHC', 'DNOW', 'DRUG', 'DSGX', 'DSM', 'DTG', 'DTSS', 'E', 'EBAY', 'ECAT', 'EDD', 'ELAB', 'ELF', 'EMA', 'ENLT', 'EPD', 'ETN', 'ETW', 'EVR', 'EXPD', 'FATBP', 'FATE', 'FBIN', 'FBYD', 'FC', 'FCFS', 'FCPT', 'FDSB', 'FDUS', 'FENC', 'FFAI', 'FFIN', 'FG', 'FIEE', 'FISI', 'FITB', 'FLG', 'FNF', 'FNLC', 'FRAF', 'FSCO', 'FTFT', 'FUN', 'FUND', 'FWRD', 'FWRG', 'GAINL', 'GAINN', 'GANX', 'GCMG', 'GEV', 'GGG', 'GHY', 'GL', 'GLADZ', 'GLP', 'GPK', 'GPMT', 'GPRO', 'GREE', 'GROW', 'HASI', 'HBT', 'HDL', 'HFFG', 'HIMS', 'HIMX', 'HMC', 'HOV', 'HP', 'HPI', 'HR', 'HSDT', 'HSHP', 'HTB', 'HTOO', 'HUBG', 'IDA', 'IEX', 'IMOS', 'INBS', 'INMB', 'INO', 'IRS', 'JOF', 'KALU', 'KB', 'KEX', 'KINS', 'KIRK', 'KLAC', 'KMX', 'KOP', 'KRKR', 'KSS', 'KYMR', 'KYN', 'LAKE', 'LAR', 'LB', 'LBRDK', 'LEN', 'LIF', 'LITB', 'LITM', 'LNG', 'LOAR', 'LXP', 'LZM', 'M', 'MAC', 'MATH', 'MATW', 'MBCN', 'MBINM', 'MBINN', 'MCR', 'MDB', 'MDT', 'MDWD', 'META', 'MFC', 'MFIN', 'MGNX', 'MGRE', 'MGRM', 'MIN', 'MIRM', 'MNDR', 'MNRO', 'MNY', 'MODG', 'MOH', 'MOS', 'MPC', 'MTDR', 'MTH', 'MUC', 'MVIS', 'MZTI', 'NABL', 'NCNO', 'NCSM', 'NEE', 'NOVT', 'NRSN', 'NRT', 'NRUC', 'NTCT', 'NUV', 'NVDA', 'NVGS', 'NWE', 'NWFL', 'OBT', 'OCCI', 'OGI', 'OIA', 'OKYO', 'OLN', 'OLP', 'OLPX', 'OM', 'OMC', 'ONL', 'ONMD', 'OPI', 'OXLCO', 'PAC', 'PAGP', 'PANL', 'PAR', 'PBA', 'PCK', 'PCM', 'PCOR', 'PDD', 'PEB', 'PEG', 'PENN', 'PETZ', 'PFL', 'PGEN', 'PLRX', 'PLXS', 'PMEC', 'PMTS', 'PMTU', 'PRAX', 'PRGO', 'PRGS', 'PRQR', 'PRS', 'PRZO', 'PSTV', 'QNCX', 'QNTM', 'QRVO', 'RCMT', 'RCON', 'RELL', 'RELY', 'RES', 'REX', 'REXR', 'RGA', 'RHI', 'RKLB', 'RNA', 'RNG', 'ROAD', 'ROG', 'RSLS', 'RXST', 'RY', 'SABR', 'SANW', 'SCI', 'SCNI', 'SCNX', 'SCS', 'SCWO', 'SES', 'SFIX', 'SFL', 'SGHC', 'SGMA', 'SHAK', 'SHCO', 'SIG', 'SKX', 'SMMT', 'SNOA', 'SOL', 'SON', 'SONO', 'SPRC', 'SSRM', 'STEP', 'STLD', 'STRL', 'STRS', 'STWD', 'SUNS', 'SWAG', 'SWIN', 'TCI', 'TEAD', 'TER', 'TFC', 'TIGO', 'TIL', 'TPB', 'TPR', 'TREE', 'TRIB', 'TRNR', 'TRV', 'TSI', 'TSLA', 'TTMI', 'U', 'UAA', 'UAL', 'UGI', 'UMBF', 'UMH', 'UPC', 'USEG', 'USPH', 'UTHR', 'UTI', 'UVV', 'VABK', 'VCTR', 'VEEV', 'VIGL', 'VIRT', 'VIV', 'VLYPP', 'VMC', 'VOR', 'VRE', 'VRRM', 'VTR', 'WBTN', 'WEYS', 'WLKP', 'WLY', 'WPM', 'WSC', 'WTTR', 'WWW', 'XERS', 'XFOR', 'XTIA', 'XYZ', 'YIBO', 'ZBAI', 'ZCMD', 'ZD', 'ZETA', 'ZKIN', 'ZTEK', 'ZYXI']
2025-07-26 08:53:08,225 - options.arbitrage - INFO -   🎯 ML model found: INMB (distance=0.105, correlation=0.905)
2025-07-26 08:53:08,225 - options.arbitrage - INFO -   ⚠️  Traditional validation failed but keeping ML result: INMB
2025-07-26 08:53:08,225 - options.arbitrage - INFO -      ML: r=0.905 (ML-only validation)
2025-07-26 08:53:08,225 - options.arbitrage - INFO - 📊 Generating comprehensive correlation chart for ZTEK
2025-07-26 08:53:08,719 - options.arbitrage - INFO - 📊 Comprehensive chart saved: results/20250726/options_charts/options_dual_validated_ZTEK_20250726_085308.png
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 🎯 ML-validated correlations for ZTEK: 1 opportunities
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 🎯 Found 18 ML-validated opportunities, selected top 9 by divergence
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 📊 Top correlation opportunities by divergence:
2025-07-26 08:53:08,720 - options.arbitrage - INFO -    1. ONMD -> FFAI: divergence=1.09, correlation=0.829
2025-07-26 08:53:08,720 - options.arbitrage - INFO -    2. RCON -> USEG: divergence=1.02, correlation=0.861
2025-07-26 08:53:08,720 - options.arbitrage - INFO -    3. ZCMD -> CNVS: divergence=0.78, correlation=0.917
2025-07-26 08:53:08,720 - options.arbitrage - INFO -    4. OPI -> SWIN: divergence=0.63, correlation=0.823
2025-07-26 08:53:08,720 - options.arbitrage - INFO -    5. MNDR -> ELAB: divergence=0.61, correlation=0.880
2025-07-26 08:53:08,720 - options.arbitrage - INFO - ✅ Found 9 correlation opportunities
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 📈 Step 2: Generating options strategies...
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 📈 Generating options strategies...
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 📊 Processing 9 correlation opportunities
2025-07-26 08:53:08,720 - options.arbitrage - INFO -   ⏭️  Skipping FFAI: NEUTRAL direction
2025-07-26 08:53:08,720 - options.arbitrage - INFO -   ⏭️  Skipping USEG: NEUTRAL direction
2025-07-26 08:53:08,720 - options.arbitrage - INFO -   ⏭️  Skipping CNVS: NEUTRAL direction
2025-07-26 08:53:08,720 - options.arbitrage - INFO -   ⏭️  Skipping SWIN: NEUTRAL direction
2025-07-26 08:53:08,720 - options.arbitrage - INFO -   ⏭️  Skipping ELAB: NEUTRAL direction
2025-07-26 08:53:08,720 - options.arbitrage - INFO -   ⏭️  Skipping RCON: NEUTRAL direction
2025-07-26 08:53:08,720 - options.arbitrage - INFO - 🎯 Generated 0 options strategies from 9 opportunities
2025-07-26 08:53:08,720 - options.arbitrage - INFO - ❌ No viable options strategies generated
2025-07-26 08:53:08,720 - stocks.trend - INFO - ✅ Options arbitrage analysis completed successfully
2025-07-26 08:53:08,721 - stocks.trend - INFO - Step 6: Managing portfolio risk with smart stop losses...
2025-07-26 08:53:08,726 - stocks.portfolio - INFO - 🔄 Starting risk management cycle...
2025-07-26 08:53:08,726 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 185...
2025-07-26 08:53:08,726 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 185...
2025-07-26 08:53:08,727 - ib_insync.client - INFO - Disconnected.
2025-07-26 08:53:08,728 - ib_insync.client - INFO - Connected
2025-07-26 08:53:08,749 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 08:53:08,754 - ib_insync.client - INFO - API connection ready
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 08:53:08,755 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 08:53:09,157 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 08:53:09,157 - IBKRClient - INFO - Connected to IBKR at 127.0.0.1:7497
2025-07-26 08:53:09,205 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 08:53:09,205 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 500...
2025-07-26 08:53:09,205 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 500...
2025-07-26 08:53:09,206 - ib_insync.client - INFO - Connected
2025-07-26 08:53:09,215 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 08:53:09,215 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 08:53:09,215 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 08:53:09,216 - ib_insync.client - INFO - API connection ready
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 08:53:09,216 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 08:53:09,519 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 08:53:09,519 - IBKRClient - INFO - Connected to IBKR at 127.0.0.1:7497
2025-07-26 08:53:09,567 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 08:53:09,568 - stocks.portfolio - INFO - 🛡️ Starting portfolio risk management...
2025-07-26 08:53:09,568 - stocks.portfolio - INFO - Managing 7 positions
2025-07-26 08:53:09,568 - stocks.risk - INFO - Stop loss calculation for DHAI:
2025-07-26 08:53:09,568 - stocks.risk - INFO -   Base stop: $0.23, Adjusted: $0.26
2025-07-26 08:53:09,568 - stocks.risk - INFO -   Base profit: $0.34, Adjusted: $0.36
2025-07-26 08:53:09,569 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:09,643 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='DHAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='DHAI', tradingClass='NMS'), order=StopOrder(orderId=5, clientId=500, action='SELL', totalQuantity=12414, auxPrice=np.float64(0.*****************)), orderStatus=OrderStatus(orderId=5, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 9, 642871, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:09,643 - IBKRClient - INFO - Placed SELL stop loss order for 12414 shares of DHAI at $0.*****************
2025-07-26 08:53:09,643 - stocks.risk - INFO - ✅ Stop loss order placed for DHAI at $0.26
2025-07-26 08:53:09,643 - stocks.risk - INFO - Stop loss calculation for TRIB:
2025-07-26 08:53:09,643 - stocks.risk - INFO -   Base stop: $0.57, Adjusted: $0.64
2025-07-26 08:53:09,643 - stocks.risk - INFO -   Base profit: $0.84, Adjusted: $0.89
2025-07-26 08:53:09,643 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:09,678 - ib_insync.wrapper - INFO - Warning 110, reqId 5: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 08:53:09,814 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='TRIB', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='TRIB', tradingClass='NMS'), order=StopOrder(orderId=7, clientId=500, action='SELL', totalQuantity=4955, auxPrice=np.float64(0.****************)), orderStatus=OrderStatus(orderId=7, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 9, 814077, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:09,814 - IBKRClient - INFO - Placed SELL stop loss order for 4955 shares of TRIB at $0.****************
2025-07-26 08:53:09,814 - stocks.risk - INFO - ✅ Stop loss order placed for TRIB at $0.64
2025-07-26 08:53:09,815 - stocks.risk - INFO - Stop loss calculation for MNDR:
2025-07-26 08:53:09,815 - stocks.risk - INFO -   Base stop: $0.91, Adjusted: $0.96
2025-07-26 08:53:09,815 - stocks.risk - INFO -   Base profit: $1.34, Adjusted: $1.38
2025-07-26 08:53:09,815 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:09,815 - ib_insync.wrapper - INFO - Warning 110, reqId 7: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 08:53:09,863 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=766915749, symbol='MNDR', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='MNDR', tradingClass='SCM'), order=StopOrder(orderId=9, clientId=500, action='SELL', totalQuantity=3115, auxPrice=np.float64(0.96407)), orderStatus=OrderStatus(orderId=9, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 9, 862810, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:09,863 - IBKRClient - INFO - Placed SELL stop loss order for 3115 shares of MNDR at $0.96407
2025-07-26 08:53:09,863 - stocks.risk - INFO - ✅ Stop loss order placed for MNDR at $0.96
2025-07-26 08:53:09,864 - stocks.risk - INFO - Stop loss calculation for SWAG:
2025-07-26 08:53:09,864 - stocks.risk - INFO -   Base stop: $1.26, Adjusted: $1.40
2025-07-26 08:53:09,864 - stocks.risk - INFO -   Base profit: $1.85, Adjusted: $1.95
2025-07-26 08:53:09,864 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 08:53:09,864 - ib_insync.wrapper - INFO - Warning 110, reqId 9: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 08:53:09,913 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=525260348, symbol='SWAG', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='SWAG', tradingClass='SCM'), order=StopOrder(orderId=11, clientId=500, action='SELL', totalQuantity=2252, auxPrice=np.float64(1.3963800000000002)), orderStatus=OrderStatus(orderId=11, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 9, 912664, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:09,913 - IBKRClient - INFO - Placed SELL stop loss order for 2252 shares of SWAG at $1.3963800000000002
2025-07-26 08:53:09,913 - stocks.risk - INFO - ✅ Stop loss order placed for SWAG at $1.40
2025-07-26 08:53:09,913 - stocks.risk - INFO - Stop loss calculation for PSTV:
2025-07-26 08:53:09,913 - stocks.risk - INFO -   Base stop: $0.45, Adjusted: $0.48
2025-07-26 08:53:09,913 - stocks.risk - INFO -   Base profit: $0.66, Adjusted: $0.68
2025-07-26 08:53:09,913 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:09,914 - ib_insync.wrapper - INFO - Warning 110, reqId 11: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 08:53:09,962 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=628816786, symbol='PSTV', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='PSTV', tradingClass='SCM'), order=StopOrder(orderId=13, clientId=500, action='SELL', totalQuantity=6291, auxPrice=np.float64(0.47734980000000005)), orderStatus=OrderStatus(orderId=13, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 9, 962238, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:09,962 - IBKRClient - INFO - Placed SELL stop loss order for 6291 shares of PSTV at $0.47734980000000005
2025-07-26 08:53:09,962 - stocks.risk - INFO - ✅ Stop loss order placed for PSTV at $0.48
2025-07-26 08:53:09,962 - stocks.risk - INFO - Stop loss calculation for ZTEK:
2025-07-26 08:53:09,962 - stocks.risk - INFO -   Base stop: $0.76, Adjusted: $0.84
2025-07-26 08:53:09,963 - stocks.risk - INFO -   Base profit: $1.11, Adjusted: $1.17
2025-07-26 08:53:09,963 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 08:53:09,964 - ib_insync.wrapper - INFO - Warning 110, reqId 13: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 08:53:10,113 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=542699775, symbol='ZTEK', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZTEK', tradingClass='SCM'), order=StopOrder(orderId=15, clientId=500, action='SELL', totalQuantity=3745, auxPrice=np.float64(0.839715)), orderStatus=OrderStatus(orderId=15, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 10, 112654, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:10,113 - IBKRClient - INFO - Placed SELL stop loss order for 3745 shares of ZTEK at $0.839715
2025-07-26 08:53:10,113 - stocks.risk - INFO - ✅ Stop loss order placed for ZTEK at $0.84
2025-07-26 08:53:10,113 - stocks.risk - INFO - Stop loss calculation for ZBAI:
2025-07-26 08:53:10,113 - stocks.risk - INFO -   Base stop: $0.36, Adjusted: $0.38
2025-07-26 08:53:10,113 - stocks.risk - INFO -   Base profit: $0.53, Adjusted: $0.54
2025-07-26 08:53:10,113 - stocks.risk - INFO -   Adjustments - Trend: 0.000, Vol: 0.250, Corr: 0.000
2025-07-26 08:53:10,114 - ib_insync.wrapper - INFO - Warning 110, reqId 15: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 08:53:10,160 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=511470153, symbol='ZBAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZBAI', tradingClass='SCM'), order=StopOrder(orderId=17, clientId=500, action='SELL', totalQuantity=7923, auxPrice=np.float64(0.3790507)), orderStatus=OrderStatus(orderId=17, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 0, 53, 10, 160285, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 08:53:10,160 - IBKRClient - INFO - Placed SELL stop loss order for 7923 shares of ZBAI at $0.3790507
2025-07-26 08:53:10,161 - stocks.risk - INFO - ✅ Stop loss order placed for ZBAI at $0.38
2025-07-26 08:53:10,161 - stocks.risk - INFO - Stop loss calculation for DHAI:
2025-07-26 08:53:10,161 - stocks.risk - INFO -   Base stop: $0.23, Adjusted: $0.26
2025-07-26 08:53:10,161 - stocks.risk - INFO -   Base profit: $0.34, Adjusted: $0.36
2025-07-26 08:53:10,161 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:10,162 - stocks.risk - INFO - Stop loss calculation for TRIB:
2025-07-26 08:53:10,162 - stocks.risk - INFO -   Base stop: $0.57, Adjusted: $0.64
2025-07-26 08:53:10,162 - stocks.risk - INFO -   Base profit: $0.84, Adjusted: $0.89
2025-07-26 08:53:10,162 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:10,162 - stocks.risk - INFO - Stop loss calculation for MNDR:
2025-07-26 08:53:10,162 - stocks.risk - INFO -   Base stop: $0.91, Adjusted: $0.96
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Base profit: $1.34, Adjusted: $1.38
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:10,163 - stocks.risk - INFO - Stop loss calculation for SWAG:
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Base stop: $1.26, Adjusted: $1.40
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Base profit: $1.85, Adjusted: $1.95
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 08:53:10,163 - stocks.risk - INFO - Stop loss calculation for PSTV:
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Base stop: $0.45, Adjusted: $0.48
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Base profit: $0.66, Adjusted: $0.68
2025-07-26 08:53:10,163 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 08:53:10,163 - stocks.risk - INFO - Stop loss calculation for ZTEK:
2025-07-26 08:53:10,164 - stocks.risk - INFO -   Base stop: $0.76, Adjusted: $0.84
2025-07-26 08:53:10,164 - stocks.risk - INFO -   Base profit: $1.11, Adjusted: $1.17
2025-07-26 08:53:10,164 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 08:53:10,164 - stocks.risk - INFO - Stop loss calculation for ZBAI:
2025-07-26 08:53:10,164 - stocks.risk - INFO -   Base stop: $0.36, Adjusted: $0.38
2025-07-26 08:53:10,164 - stocks.risk - INFO -   Base profit: $0.53, Adjusted: $0.54
2025-07-26 08:53:10,164 - stocks.risk - INFO -   Adjustments - Trend: 0.000, Vol: 0.250, Corr: 0.000
2025-07-26 08:53:10,164 - stocks.portfolio - INFO - ✅ Portfolio risk management completed
2025-07-26 08:53:10,164 - stocks.portfolio - INFO - 📊 Portfolio Summary:
2025-07-26 08:53:10,164 - stocks.portfolio - INFO -   Total Value: $23,331.13
2025-07-26 08:53:10,164 - stocks.portfolio - INFO -   Total P&L: $0.00 (0.0%)
2025-07-26 08:53:10,164 - stocks.portfolio - INFO -   Risk Level: LOW
2025-07-26 08:53:10,164 - stocks.portfolio - INFO -   Stop Loss Coverage: 100.0%
2025-07-26 08:53:10,165 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 712 B sent in 9 messages, 34.2 kB received in 744 messages, session time 1.44 s.
2025-07-26 08:53:10,165 - ib_insync.client - INFO - Disconnecting
2025-07-26 08:53:10,165 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 08:53:10,165 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 2.60 kB sent in 23 messages, 39.3 kB received in 673 messages, session time 960 ms.
2025-07-26 08:53:10,165 - ib_insync.client - INFO - Disconnecting
2025-07-26 08:53:10,165 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 08:53:10,165 - stocks.trend - INFO - ✅ Portfolio risk management completed successfully
2025-07-26 08:53:10,165 - stocks.trend - INFO - 📊 Portfolio Value: $23,331.13
2025-07-26 08:53:10,165 - stocks.trend - INFO - 💰 Total P&L: $0.00 (0.0%)
2025-07-26 08:53:10,166 - stocks.trend - INFO - 🛡️ Risk Level: LOW
2025-07-26 08:53:10,166 - stocks.trend - INFO - 📋 Stop Loss Orders: 7
2025-07-26 08:53:10,166 - stocks.trend - INFO - 💡 Risk Management Recommendations:
2025-07-26 08:53:10,166 - stocks.trend - INFO -   ✅ Portfolio risk appears well managed
2025-07-26 08:53:10,185 - stocks.trend - INFO - Loaded classification info for 457 stocks from stock_info.csv
2025-07-26 08:53:10,200 - stocks.trend - INFO - 📊 Daily results saved to results/20250726/daily_results_20250726.csv
2025-07-26 08:53:10,200 - stocks.trend - INFO - ============================================================
2025-07-26 08:53:10,200 - stocks.trend - INFO - Daily trading analysis completed successfully!
2025-07-26 08:53:10,200 - __main__ - INFO - ✅ Trading analysis completed successfully
2025-07-26 08:53:10,204 - __main__ - INFO - ============================================================
2025-07-26 08:53:10,204 - __main__ - INFO - 🎉 FULL TRADING WORKFLOW COMPLETED SUCCESSFULLY!
2025-07-26 08:53:10,204 - __main__ - INFO - ⏱️ Total time: 00:00:27
2025-07-26 08:53:10,205 - __main__ - INFO - 📊 End time: 2025-07-26 08:53:10
2025-07-26 08:53:10,205 - __main__ - INFO - ============================================================
2025-07-26 08:53:10,205 - ib_insync.client - INFO - Disconnected.
2025-07-26 08:53:10,205 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:03:07,431 - stocks.trend - INFO - 📝 Logging configured: results/20250726/logs/ibkr_trading.log
2025-07-26 09:03:07,431 - __main__ - INFO - 🚀 开始完整交易分析...
2025-07-26 09:03:07,431 - stocks.trend - INFO - 🚀 Starting daily trading analysis workflow...
2025-07-26 09:03:07,431 - stocks.trend - INFO - 🔧 System configuration: enable_options=False
2025-07-26 09:03:07,431 - stocks.trend - INFO - ============================================================
2025-07-26 09:03:07,431 - stocks.trend - INFO - Skipping stock universe update (use --update-universe to enable)
2025-07-26 09:03:07,431 - stocks.trend - INFO - Step 1: Downloading data for all stocks...
2025-07-26 09:03:07,431 - stocks.trend - INFO - Found 457 symbols to analyze
2025-07-26 09:03:07,431 - stocks.trend - INFO - Downloading data for ALL 457 symbols to maximize correlation opportunities
2025-07-26 09:03:07,431 - stocks.trend - INFO - Downloading data from 2024-07-26 to 2025-07-26
2025-07-26 09:03:07,431 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:03:07,431 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:03:07,432 - ib_insync.client - INFO - Connected
2025-07-26 09:03:07,481 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:03:07,484 - ib_insync.client - INFO - API connection ready
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:03:07,485 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:03:07,486 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:03:07,890 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:03:07,891 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 788
2025-07-26 09:03:09,961 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:03:09,963 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:03:09,963 - core.market_data - INFO - Connected to IBKR for sequential downloading (optimized for data farm stability)
2025-07-26 09:03:09,963 - core.market_data - INFO - 🚀 Starting optimized concurrent download
2025-07-26 09:03:09,963 - core.market_data - INFO - 📊 Total stocks: 457 (cached: 0, downloading: 457)
2025-07-26 09:03:09,963 - core.market_data - INFO - ⚡ Adaptive concurrent limit: 1
2025-07-26 09:03:09,963 - core.market_data - INFO - 📅 Date range: 2024-07-26 to 2025-07-26
2025-07-26 09:03:09,964 - core.market_data - INFO - 📂 Progress loaded: 456 completed, 1 failed, 0 invalid
2025-07-26 09:03:09,964 - core.market_data - INFO - 🔄 Resuming download from symbol 457/457
2025-07-26 09:03:09,964 - core.market_data - INFO - 📊 Previous progress: 456 completed, 1 failed
2025-07-26 09:03:09,964 - core.market_data - INFO - ✅ All data available from previous downloads, loading from incremental data...
2025-07-26 09:03:09,982 - core.market_data - INFO - 📊 Loading stock classifications from data/stock_info/stock_info.csv
2025-07-26 09:03:10,005 - core.market_data - INFO - ✅ Updated classifications for 457 stocks
2025-07-26 09:03:10,005 - core.market_data - INFO - 📦 Loaded 401 stocks from incremental data
2025-07-26 09:03:10,005 - stocks.trend - INFO - ✅ IBKR connection transferred to main client - keeping alive for trading
2025-07-26 09:03:10,005 - stocks.trend - INFO - Successfully downloaded data for 401 stocks
2025-07-26 09:03:10,005 - stocks.trend - INFO - Data period: 2024-02-09 00:00:00 to 2025-07-25 00:00:00
2025-07-26 09:03:10,005 - stocks.trend - INFO - Step 2: Running ML model analysis...
2025-07-26 09:03:10,006 - stocks.trend - WARNING - Price data is a list (inhomogeneous shapes detected)
2025-07-26 09:03:10,006 - stocks.trend - INFO - Number of stocks: 401
2025-07-26 09:03:10,006 - stocks.trend - INFO - Price array lengths: min=246, max=365
2025-07-26 09:03:10,019 - stocks.trend - INFO - ✅ Selected 401 stocks with sufficient data (60 days each)
2025-07-26 09:03:10,019 - stocks.trend - INFO - ❌ Rejected 0 stocks with insufficient data (< 60 days)
2025-07-26 09:03:10,019 - stocks.trend - INFO - 📊 Data quality: Using only stocks with robust historical data for scientific analysis
2025-07-26 09:03:10,021 - stocks.trend - INFO - Initial data shape: (401, 60)
2025-07-26 09:03:10,025 - stocks.trend - INFO - Price data range: min=0.1513, max=937.7600
2025-07-26 09:03:10,042 - stocks.trend - INFO - NaN count: 0, Zero count: 0
2025-07-26 09:03:10,043 - stocks.trend - INFO - Performing advanced data cleaning...
2025-07-26 09:03:10,075 - stocks.trend - INFO - Good data quality (0.0%), using relaxed validation for more stocks
2025-07-26 09:03:10,094 - stocks.trend - INFO - Data cleaning results:
2025-07-26 09:03:10,094 - stocks.trend - INFO -   Original stocks: 401
2025-07-26 09:03:10,094 - stocks.trend - INFO -   Valid stocks after cleaning: 401
2025-07-26 09:03:10,094 - stocks.trend - INFO -   Rejection rate: 0.0%
2025-07-26 09:03:10,095 - stocks.trend - INFO - Filtered to 401 stocks with valid price data (from 401)
2025-07-26 09:03:10,096 - stocks.trend - INFO - Final dataset: 401 stocks ready for analysis
2025-07-26 09:03:10,097 - stocks.trend - INFO - Training model with 401 stocks, 60 time periods
2025-07-26 09:03:10,099 - stocks.trend - INFO - Data consistency check:
2025-07-26 09:03:10,099 - stocks.trend - INFO -   Stocks in price data: 401
2025-07-26 09:03:10,099 - stocks.trend - INFO -   Stocks in hierarchical info: 401
2025-07-26 09:03:10,099 - stocks.trend - INFO -   Sectors dict length: 401
2025-07-26 09:03:10,099 - stocks.trend - INFO -   Industries dict length: 401
2025-07-26 09:03:10,099 - stocks.trend - INFO - Training MSIS-MCS model...
2025-07-26 09:03:10,099 - stocks.trend - INFO - Final training data: logp shape=(401, 60), info stocks=401
2025-07-26 09:03:15,375 - core.stats - INFO - Estimating log price statistics: phi shape=(401, 16), psi shape=(401, 1), tt_pred shape=(16, 6)
2025-07-26 09:03:15,378 - core.stats - INFO - Estimated log price predictions: shape=(401, 6), std shape=(401, 6)
2025-07-26 09:03:15,378 - core.stats - INFO - Estimated price predictions: shape=(401, 6), std shape=(401, 6)
2025-07-26 09:03:15,379 - stocks.trend - INFO - 📊 Trend Score Distribution Analysis:
2025-07-26 09:03:15,379 - stocks.trend - INFO -   Min: -7.436, Max: 3.329
2025-07-26 09:03:15,379 - stocks.trend - INFO -   Mean: -2.998, Std: 1.501
2025-07-26 09:03:15,379 - stocks.trend - INFO -    1th percentile: -5.600
2025-07-26 09:03:15,379 - stocks.trend - INFO -    5th percentile: -4.609
2025-07-26 09:03:15,379 - stocks.trend - INFO -   10th percentile: -4.274
2025-07-26 09:03:15,379 - stocks.trend - INFO -   25th percentile: -3.924
2025-07-26 09:03:15,379 - stocks.trend - INFO -   50th percentile: -3.416
2025-07-26 09:03:15,379 - stocks.trend - INFO -   75th percentile: -2.544
2025-07-26 09:03:15,379 - stocks.trend - INFO -   90th percentile: -0.936
2025-07-26 09:03:15,379 - stocks.trend - INFO -   95th percentile: -0.222
2025-07-26 09:03:15,379 - stocks.trend - INFO -   99th percentile:  2.191
2025-07-26 09:03:15,380 - stocks.trend - INFO - 📈 Stock Count by Range:
2025-07-26 09:03:15,380 - stocks.trend - INFO -   < -5.0      :   10 stocks (  2.5%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   -5.0 to -4.0:   74 stocks ( 18.5%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   -4.0 to -3.0:  172 stocks ( 42.9%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   -3.0 to -2.5:   47 stocks ( 11.7%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   -2.5 to -2.0:   16 stocks (  4.0%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   -2.0 to -1.0:   37 stocks (  9.2%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   -1.0 to 0.0 :   28 stocks (  7.0%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   0.0 to 1.0  :    7 stocks (  1.7%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   1.0 to 2.0  :    4 stocks (  1.0%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   2.0 to 2.5  :    3 stocks (  0.7%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   2.5 to 3.0  :    1 stocks (  0.2%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   3.0 to 4.0  :    2 stocks (  0.5%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   4.0 to 5.0  :    0 stocks (  0.0%)
2025-07-26 09:03:15,380 - stocks.trend - INFO -   > 5.0       :    0 stocks (  0.0%)
2025-07-26 09:03:15,380 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,380 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,381 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,381 - stocks.trend - INFO - Using 'high' extreme level with bounds: {'EXTREME BELOW TREND': np.float64(-10.600149607442054), 'HIGHLY BELOW TREND': np.float64(-5.600149607442054), 'BELOW TREND': np.float64(-4.609490217814757), 'ALONG TREND': np.float64(-0.9360105085588056), 'ABOVE TREND': np.float64(-0.2224926661659874), 'HIGHLY ABOVE TREND': np.float64(2.1908108931509997), 'EXTREME ABOVE TREND': inf}
2025-07-26 09:03:15,381 - stocks.trend - INFO - 🔍 Generating ML correlation matches for options trading...
2025-07-26 09:03:15,385 - core.stats - INFO - Estimated matches for 401 stocks
2025-07-26 09:03:15,385 - stocks.trend - INFO - ✅ Generated 401 ML correlation matches
2025-07-26 09:03:15,388 - stocks.trend - INFO - Model analysis completed successfully
2025-07-26 09:03:15,388 - stocks.trend - INFO - Stocks above trend: 40
2025-07-26 09:03:15,388 - stocks.trend - INFO - Stocks below trend: 20
2025-07-26 09:03:15,388 - stocks.trend - INFO - Stocks along trend: 341
2025-07-26 09:03:15,389 - stocks.trend - INFO - Step 3: Performing correlation analysis...
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 0 stocks EXTREME BELOW trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 4 stocks HIGHLY BELOW trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 16 stocks BELOW trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 341 stocks ALONG trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 20 stocks ABOVE trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 16 stocks HIGHLY ABOVE trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Found 4 stocks EXTREME ABOVE trend
2025-07-26 09:03:15,389 - stocks.trend - INFO - Identified 0 correlation opportunities
2025-07-26 09:03:15,389 - stocks.trend - INFO - Step 4: Executing trades based on correlation analysis...
2025-07-26 09:03:15,389 - stocks.trend - INFO - ✅ Using existing IBKR connection for trading
2025-07-26 09:03:15,389 - stocks.trend - INFO - Betty bot prefers strong stocks - prioritizing EXTREME ABOVE TREND candidates
2025-07-26 09:03:15,390 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,390 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,390 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,390 - stocks.trend - INFO - 🔥 ULTRA RARE: ONMD (score: 3.33)
2025-07-26 09:03:15,390 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,390 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,390 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,390 - stocks.trend - INFO - 🔥 ULTRA RARE: OPI (score: 3.07)
2025-07-26 09:03:15,391 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,391 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,391 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,391 - stocks.trend - INFO - 🔥 ULTRA RARE: ZCMD (score: 2.93)
2025-07-26 09:03:15,391 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,391 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,391 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,391 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,391 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,391 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,391 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,391 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,391 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,392 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,392 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,392 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,392 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,392 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,392 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,392 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,392 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,392 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,392 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:03:15,392 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:03:15,392 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:03:15,392 - stocks.trend - INFO - Executing trades for 10 stocks...
2025-07-26 09:03:15,393 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:03:15,393 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:03:15,393 - ib_insync.client - INFO - Connected
2025-07-26 09:03:15,421 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:03:15,424 - ib_insync.wrapper - ERROR - Error 326, reqId -1: \u5ba2\u6237\u53f7\u7801\u5df2\u88ab\u4f7f\u7528\uff0c\u65e0\u6cd5\u8fde\u63a5\u3002\u8bf7\u7528\u672a\u88ab\u4f7f\u7528\u7684\u5ba2\u6237\u53f7\u7801\u91cd\u8bd5\u3002
2025-07-26 09:03:15,929 - ib_insync.client - ERROR - Peer closed connection. clientId 788 already in use?
2025-07-26 09:03:45,396 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:03:45,397 - ib_insync.client - ERROR - API connection failed: TimeoutError()
2025-07-26 09:03:45,398 - IBKRClient - ERROR - Failed to connect to IBKR: 
2025-07-26 09:03:50,401 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:03:50,402 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:03:50,403 - ib_insync.client - INFO - Connected
2025-07-26 09:03:50,434 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:03:50,437 - ib_insync.wrapper - ERROR - Error 326, reqId -1: \u5ba2\u6237\u53f7\u7801\u5df2\u88ab\u4f7f\u7528\uff0c\u65e0\u6cd5\u8fde\u63a5\u3002\u8bf7\u7528\u672a\u88ab\u4f7f\u7528\u7684\u5ba2\u6237\u53f7\u7801\u91cd\u8bd5\u3002
2025-07-26 09:03:50,941 - ib_insync.client - ERROR - Peer closed connection. clientId 788 already in use?
2025-07-26 09:04:20,407 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:04:20,409 - ib_insync.client - ERROR - API connection failed: TimeoutError()
2025-07-26 09:04:20,410 - IBKRClient - ERROR - Failed to connect to IBKR: 
2025-07-26 09:04:23,536 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:04:25,428 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:04:25,429 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:04:25,431 - ib_insync.client - INFO - Connected
2025-07-26 09:04:25,450 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:04:25,451 - ib_insync.wrapper - ERROR - Error 326, reqId -1: \u5ba2\u6237\u53f7\u7801\u5df2\u88ab\u4f7f\u7528\uff0c\u65e0\u6cd5\u8fde\u63a5\u3002\u8bf7\u7528\u672a\u88ab\u4f7f\u7528\u7684\u5ba2\u6237\u53f7\u7801\u91cd\u8bd5\u3002
2025-07-26 09:04:25,957 - ib_insync.client - ERROR - Peer closed connection. clientId 788 already in use?
2025-07-26 09:04:55,436 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:04:55,439 - ib_insync.client - ERROR - API connection failed: TimeoutError()
2025-07-26 09:04:55,440 - IBKRClient - ERROR - Failed to connect to IBKR: 
2025-07-26 09:04:55,440 - stocks.trend - WARNING - ❌ Failed to connect bot to IBKR - trades will remain in simulation mode
2025-07-26 09:04:55,441 - stocks.trend - INFO - Trading completed:
2025-07-26 09:04:55,441 - stocks.trend - INFO - Portfolio value: $50000.00
2025-07-26 09:04:55,441 - stocks.trend - INFO - Cash: $38336.23
2025-07-26 09:04:55,441 - stocks.trend - INFO - Invested: $11663.77
2025-07-26 09:04:55,441 - stocks.trend - INFO - Positions: 7
2025-07-26 09:04:55,443 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 712 B sent in 9 messages, 30.6 kB received in 664 messages, session time 108 s.
2025-07-26 09:04:55,443 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:04:55,444 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:04:55,444 - stocks.trend - INFO - 🔍 Checking options arbitrage: enable_options=False
2025-07-26 09:04:55,444 - stocks.trend - INFO - ❌ Options arbitrage is DISABLED - skipping Step 5
2025-07-26 09:04:55,444 - stocks.trend - INFO - Step 6: Managing portfolio risk with smart stop losses...
2025-07-26 09:04:55,468 - stocks.portfolio - INFO - 🔄 Starting risk management cycle...
2025-07-26 09:04:55,470 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:04:55,470 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 788...
2025-07-26 09:04:55,471 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:04:55,472 - ib_insync.client - INFO - Connected
2025-07-26 09:04:55,482 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:04:55,484 - ib_insync.client - INFO - API connection ready
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:04:55,485 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:04:55,886 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:04:55,886 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 788
2025-07-26 09:04:57,943 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:04:57,944 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:04:57,944 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 500...
2025-07-26 09:04:57,944 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 500...
2025-07-26 09:04:57,945 - ib_insync.client - INFO - Connected
2025-07-26 09:04:57,959 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:04:57,959 - ib_insync.client - INFO - API connection ready
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:04:57,960 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:04:58,261 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:04:58,261 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 500
2025-07-26 09:05:00,315 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:05:00,315 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:05:00,315 - stocks.portfolio - INFO - 🛡️ Starting portfolio risk management...
2025-07-26 09:05:00,315 - stocks.portfolio - INFO - Managing 7 positions
2025-07-26 09:05:00,316 - stocks.risk - INFO - Stop loss calculation for TRIB:
2025-07-26 09:05:00,316 - stocks.risk - INFO -   Base stop: $0.57, Adjusted: $0.64
2025-07-26 09:05:00,317 - stocks.risk - INFO -   Base profit: $0.84, Adjusted: $0.89
2025-07-26 09:05:00,317 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,380 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='TRIB', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='TRIB', tradingClass='NMS'), order=StopOrder(orderId=22, clientId=500, action='SELL', totalQuantity=2477, auxPrice=np.float64(0.****************)), orderStatus=OrderStatus(orderId=22, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 379773, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,380 - IBKRClient - INFO - Placed SELL stop loss order for 2477 shares of TRIB at $0.****************
2025-07-26 09:05:00,380 - stocks.risk - INFO - ✅ Stop loss order placed for TRIB at $0.64
2025-07-26 09:05:00,381 - stocks.risk - INFO - Stop loss calculation for DHAI:
2025-07-26 09:05:00,381 - stocks.risk - INFO -   Base stop: $0.23, Adjusted: $0.26
2025-07-26 09:05:00,381 - stocks.risk - INFO -   Base profit: $0.34, Adjusted: $0.36
2025-07-26 09:05:00,381 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,391 - ib_insync.wrapper - INFO - Warning 110, reqId 22: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:05:00,533 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='DHAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='DHAI', tradingClass='NMS'), order=StopOrder(orderId=24, clientId=500, action='SELL', totalQuantity=6207, auxPrice=np.float64(0.*****************)), orderStatus=OrderStatus(orderId=24, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 533428, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,534 - IBKRClient - INFO - Placed SELL stop loss order for 6207 shares of DHAI at $0.*****************
2025-07-26 09:05:00,534 - stocks.risk - INFO - ✅ Stop loss order placed for DHAI at $0.26
2025-07-26 09:05:00,534 - stocks.risk - INFO - Stop loss calculation for MNDR:
2025-07-26 09:05:00,535 - stocks.risk - INFO -   Base stop: $0.91, Adjusted: $0.96
2025-07-26 09:05:00,535 - stocks.risk - INFO -   Base profit: $1.34, Adjusted: $1.38
2025-07-26 09:05:00,535 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,535 - ib_insync.wrapper - INFO - Warning 110, reqId 24: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:05:00,581 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=766915749, symbol='MNDR', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='MNDR', tradingClass='SCM'), order=StopOrder(orderId=26, clientId=500, action='SELL', totalQuantity=1557, auxPrice=np.float64(0.96407)), orderStatus=OrderStatus(orderId=26, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 581300, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,582 - IBKRClient - INFO - Placed SELL stop loss order for 1557 shares of MNDR at $0.96407
2025-07-26 09:05:00,582 - stocks.risk - INFO - ✅ Stop loss order placed for MNDR at $0.96
2025-07-26 09:05:00,583 - stocks.risk - INFO - Stop loss calculation for SWAG:
2025-07-26 09:05:00,583 - stocks.risk - INFO -   Base stop: $1.26, Adjusted: $1.40
2025-07-26 09:05:00,583 - stocks.risk - INFO -   Base profit: $1.85, Adjusted: $1.95
2025-07-26 09:05:00,583 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:05:00,583 - ib_insync.wrapper - INFO - Warning 110, reqId 26: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:05:00,631 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=525260348, symbol='SWAG', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='SWAG', tradingClass='SCM'), order=StopOrder(orderId=28, clientId=500, action='SELL', totalQuantity=1126, auxPrice=np.float64(1.3963800000000002)), orderStatus=OrderStatus(orderId=28, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 631209, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,631 - IBKRClient - INFO - Placed SELL stop loss order for 1126 shares of SWAG at $1.3963800000000002
2025-07-26 09:05:00,631 - stocks.risk - INFO - ✅ Stop loss order placed for SWAG at $1.40
2025-07-26 09:05:00,632 - stocks.risk - INFO - Stop loss calculation for PSTV:
2025-07-26 09:05:00,632 - stocks.risk - INFO -   Base stop: $0.45, Adjusted: $0.48
2025-07-26 09:05:00,632 - stocks.risk - INFO -   Base profit: $0.66, Adjusted: $0.68
2025-07-26 09:05:00,632 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,632 - ib_insync.wrapper - INFO - Warning 110, reqId 28: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:05:00,680 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=628816786, symbol='PSTV', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='PSTV', tradingClass='SCM'), order=StopOrder(orderId=30, clientId=500, action='SELL', totalQuantity=3145, auxPrice=np.float64(0.47734980000000005)), orderStatus=OrderStatus(orderId=30, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 680257, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,681 - IBKRClient - INFO - Placed SELL stop loss order for 3145 shares of PSTV at $0.47734980000000005
2025-07-26 09:05:00,681 - stocks.risk - INFO - ✅ Stop loss order placed for PSTV at $0.48
2025-07-26 09:05:00,681 - stocks.risk - INFO - Stop loss calculation for ZTEK:
2025-07-26 09:05:00,681 - stocks.risk - INFO -   Base stop: $0.76, Adjusted: $0.84
2025-07-26 09:05:00,681 - stocks.risk - INFO -   Base profit: $1.11, Adjusted: $1.17
2025-07-26 09:05:00,681 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:05:00,682 - ib_insync.wrapper - INFO - Warning 110, reqId 30: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:05:00,731 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=542699775, symbol='ZTEK', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZTEK', tradingClass='SCM'), order=StopOrder(orderId=32, clientId=500, action='SELL', totalQuantity=1872, auxPrice=np.float64(0.839715)), orderStatus=OrderStatus(orderId=32, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 731056, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,731 - IBKRClient - INFO - Placed SELL stop loss order for 1872 shares of ZTEK at $0.839715
2025-07-26 09:05:00,731 - stocks.risk - INFO - ✅ Stop loss order placed for ZTEK at $0.84
2025-07-26 09:05:00,732 - stocks.risk - INFO - Stop loss calculation for ZBAI:
2025-07-26 09:05:00,732 - stocks.risk - INFO -   Base stop: $0.36, Adjusted: $0.38
2025-07-26 09:05:00,732 - stocks.risk - INFO -   Base profit: $0.53, Adjusted: $0.54
2025-07-26 09:05:00,732 - stocks.risk - INFO -   Adjustments - Trend: 0.000, Vol: 0.250, Corr: 0.000
2025-07-26 09:05:00,734 - ib_insync.wrapper - INFO - Warning 110, reqId 32: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:05:00,879 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=511470153, symbol='ZBAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZBAI', tradingClass='SCM'), order=StopOrder(orderId=34, clientId=500, action='SELL', totalQuantity=3961, auxPrice=np.float64(0.3790507)), orderStatus=OrderStatus(orderId=34, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 5, 0, 879400, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:05:00,880 - IBKRClient - INFO - Placed SELL stop loss order for 3961 shares of ZBAI at $0.3790507
2025-07-26 09:05:00,880 - stocks.risk - INFO - ✅ Stop loss order placed for ZBAI at $0.38
2025-07-26 09:05:00,880 - stocks.risk - INFO - Stop loss calculation for TRIB:
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Base stop: $0.57, Adjusted: $0.64
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Base profit: $0.84, Adjusted: $0.89
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,881 - stocks.risk - INFO - Stop loss calculation for DHAI:
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Base stop: $0.23, Adjusted: $0.26
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Base profit: $0.34, Adjusted: $0.36
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,881 - stocks.risk - INFO - Stop loss calculation for MNDR:
2025-07-26 09:05:00,881 - stocks.risk - INFO -   Base stop: $0.91, Adjusted: $0.96
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base profit: $1.34, Adjusted: $1.38
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,882 - stocks.risk - INFO - Stop loss calculation for SWAG:
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base stop: $1.26, Adjusted: $1.40
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base profit: $1.85, Adjusted: $1.95
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:05:00,882 - stocks.risk - INFO - Stop loss calculation for PSTV:
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base stop: $0.45, Adjusted: $0.48
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base profit: $0.66, Adjusted: $0.68
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:05:00,882 - stocks.risk - INFO - Stop loss calculation for ZTEK:
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base stop: $0.76, Adjusted: $0.84
2025-07-26 09:05:00,882 - stocks.risk - INFO -   Base profit: $1.11, Adjusted: $1.17
2025-07-26 09:05:00,883 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:05:00,883 - stocks.risk - INFO - Stop loss calculation for ZBAI:
2025-07-26 09:05:00,883 - stocks.risk - INFO -   Base stop: $0.36, Adjusted: $0.38
2025-07-26 09:05:00,883 - stocks.risk - INFO -   Base profit: $0.53, Adjusted: $0.54
2025-07-26 09:05:00,883 - stocks.risk - INFO -   Adjustments - Trend: 0.000, Vol: 0.250, Corr: 0.000
2025-07-26 09:05:00,883 - stocks.portfolio - INFO - ✅ Portfolio risk management completed
2025-07-26 09:05:00,883 - stocks.portfolio - INFO - 📊 Portfolio Summary:
2025-07-26 09:05:00,883 - stocks.portfolio - INFO -   Total Value: $11,663.77
2025-07-26 09:05:00,883 - stocks.portfolio - INFO -   Total P&L: $0.00 (0.0%)
2025-07-26 09:05:00,883 - stocks.portfolio - INFO -   Risk Level: LOW
2025-07-26 09:05:00,883 - stocks.portfolio - INFO -   Stop Loss Coverage: 100.0%
2025-07-26 09:05:00,884 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 712 B sent in 9 messages, 34.2 kB received in 744 messages, session time 5.41 s.
2025-07-26 09:05:00,884 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:05:00,884 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:05:00,884 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 2.61 kB sent in 23 messages, 39.8 kB received in 673 messages, session time 2.94 s.
2025-07-26 09:05:00,884 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:05:00,884 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:05:00,884 - stocks.trend - INFO - ✅ Portfolio risk management completed successfully
2025-07-26 09:05:00,884 - stocks.trend - INFO - 📊 Portfolio Value: $11,663.77
2025-07-26 09:05:00,884 - stocks.trend - INFO - 💰 Total P&L: $0.00 (0.0%)
2025-07-26 09:05:00,884 - stocks.trend - INFO - 🛡️ Risk Level: LOW
2025-07-26 09:05:00,884 - stocks.trend - INFO - 📋 Stop Loss Orders: 7
2025-07-26 09:05:00,884 - stocks.trend - INFO - 💡 Risk Management Recommendations:
2025-07-26 09:05:00,884 - stocks.trend - INFO -   ✅ Portfolio risk appears well managed
2025-07-26 09:05:00,914 - stocks.trend - INFO - Loaded classification info for 457 stocks from stock_info.csv
2025-07-26 09:05:00,926 - stocks.trend - INFO - 📊 Daily results saved to results/20250726/daily_results_20250726.csv
2025-07-26 09:05:00,926 - stocks.trend - INFO - ============================================================
2025-07-26 09:05:00,926 - stocks.trend - INFO - Daily trading analysis completed successfully!
2025-07-26 09:05:00,927 - __main__ - INFO - ✅ 交易分析成功完成
2025-07-26 09:05:00,930 - __main__ - INFO - ============================================================
2025-07-26 09:05:00,930 - __main__ - INFO - 🎉 完整交易工作流成功完成！
2025-07-26 09:05:00,930 - __main__ - INFO - ⏱️ 总时间: 00:01:57
2025-07-26 09:05:00,930 - __main__ - INFO - 📊 结束时间: 2025-07-26 09:05:00
2025-07-26 09:05:00,930 - __main__ - INFO - ============================================================
2025-07-26 09:05:00,930 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:05:00,930 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:06:05,751 - stocks.trend - INFO - 📝 Logging configured: results/20250726/logs/ibkr_trading.log
2025-07-26 09:06:05,751 - __main__ - INFO - 🚀 开始完整交易分析...
2025-07-26 09:06:05,751 - stocks.trend - INFO - 🚀 Starting daily trading analysis workflow...
2025-07-26 09:06:05,751 - stocks.trend - INFO - 🔧 System configuration: enable_options=False
2025-07-26 09:06:05,751 - stocks.trend - INFO - ============================================================
2025-07-26 09:06:05,752 - stocks.trend - INFO - Skipping stock universe update (use --update-universe to enable)
2025-07-26 09:06:05,752 - stocks.trend - INFO - Step 1: Downloading data for all stocks...
2025-07-26 09:06:05,752 - stocks.trend - INFO - Found 457 symbols to analyze
2025-07-26 09:06:05,752 - stocks.trend - INFO - Downloading data for ALL 457 symbols to maximize correlation opportunities
2025-07-26 09:06:05,752 - stocks.trend - INFO - Downloading data from 2024-07-26 to 2025-07-26
2025-07-26 09:06:05,752 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 982...
2025-07-26 09:06:05,752 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 982...
2025-07-26 09:06:05,752 - ib_insync.client - INFO - Connected
2025-07-26 09:06:05,771 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:06:05,773 - ib_insync.client - INFO - API connection ready
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:06:05,773 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:06:06,175 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:06:06,176 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 982
2025-07-26 09:06:08,251 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:06:08,251 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:06:08,251 - core.market_data - INFO - Connected to IBKR for sequential downloading (optimized for data farm stability)
2025-07-26 09:06:08,252 - core.market_data - INFO - 🚀 Starting optimized concurrent download
2025-07-26 09:06:08,252 - core.market_data - INFO - 📊 Total stocks: 457 (cached: 0, downloading: 457)
2025-07-26 09:06:08,252 - core.market_data - INFO - ⚡ Adaptive concurrent limit: 1
2025-07-26 09:06:08,252 - core.market_data - INFO - 📅 Date range: 2024-07-26 to 2025-07-26
2025-07-26 09:06:08,253 - core.market_data - INFO - 📂 Progress loaded: 456 completed, 1 failed, 0 invalid
2025-07-26 09:06:08,253 - core.market_data - INFO - 🔄 Resuming download from symbol 457/457
2025-07-26 09:06:08,253 - core.market_data - INFO - 📊 Previous progress: 456 completed, 1 failed
2025-07-26 09:06:08,254 - core.market_data - INFO - ✅ All data available from previous downloads, loading from incremental data...
2025-07-26 09:06:08,272 - core.market_data - INFO - 📊 Loading stock classifications from data/stock_info/stock_info.csv
2025-07-26 09:06:08,299 - core.market_data - INFO - ✅ Updated classifications for 457 stocks
2025-07-26 09:06:08,299 - core.market_data - INFO - 📦 Loaded 401 stocks from incremental data
2025-07-26 09:06:08,299 - stocks.trend - INFO - ✅ IBKR connection transferred to main client - keeping alive for trading
2025-07-26 09:06:08,299 - stocks.trend - INFO - Successfully downloaded data for 401 stocks
2025-07-26 09:06:08,300 - stocks.trend - INFO - Data period: 2024-02-09 00:00:00 to 2025-07-25 00:00:00
2025-07-26 09:06:08,300 - stocks.trend - INFO - Step 2: Running ML model analysis...
2025-07-26 09:06:08,300 - stocks.trend - WARNING - Price data is a list (inhomogeneous shapes detected)
2025-07-26 09:06:08,300 - stocks.trend - INFO - Number of stocks: 401
2025-07-26 09:06:08,300 - stocks.trend - INFO - Price array lengths: min=246, max=365
2025-07-26 09:06:08,306 - stocks.trend - INFO - ✅ Selected 401 stocks with sufficient data (60 days each)
2025-07-26 09:06:08,306 - stocks.trend - INFO - ❌ Rejected 0 stocks with insufficient data (< 60 days)
2025-07-26 09:06:08,306 - stocks.trend - INFO - 📊 Data quality: Using only stocks with robust historical data for scientific analysis
2025-07-26 09:06:08,306 - stocks.trend - INFO - Initial data shape: (401, 60)
2025-07-26 09:06:08,306 - stocks.trend - INFO - Price data range: min=0.1513, max=937.7600
2025-07-26 09:06:08,307 - stocks.trend - INFO - NaN count: 0, Zero count: 0
2025-07-26 09:06:08,307 - stocks.trend - INFO - Performing advanced data cleaning...
2025-07-26 09:06:08,311 - stocks.trend - INFO - Good data quality (0.0%), using relaxed validation for more stocks
2025-07-26 09:06:08,317 - stocks.trend - INFO - Data cleaning results:
2025-07-26 09:06:08,317 - stocks.trend - INFO -   Original stocks: 401
2025-07-26 09:06:08,317 - stocks.trend - INFO -   Valid stocks after cleaning: 401
2025-07-26 09:06:08,317 - stocks.trend - INFO -   Rejection rate: 0.0%
2025-07-26 09:06:08,317 - stocks.trend - INFO - Filtered to 401 stocks with valid price data (from 401)
2025-07-26 09:06:08,319 - stocks.trend - INFO - Final dataset: 401 stocks ready for analysis
2025-07-26 09:06:08,319 - stocks.trend - INFO - Training model with 401 stocks, 60 time periods
2025-07-26 09:06:08,322 - stocks.trend - INFO - Data consistency check:
2025-07-26 09:06:08,322 - stocks.trend - INFO -   Stocks in price data: 401
2025-07-26 09:06:08,322 - stocks.trend - INFO -   Stocks in hierarchical info: 401
2025-07-26 09:06:08,322 - stocks.trend - INFO -   Sectors dict length: 401
2025-07-26 09:06:08,322 - stocks.trend - INFO -   Industries dict length: 401
2025-07-26 09:06:08,323 - stocks.trend - INFO - Training MSIS-MCS model...
2025-07-26 09:06:08,323 - stocks.trend - INFO - Final training data: logp shape=(401, 60), info stocks=401
2025-07-26 09:06:13,490 - core.stats - INFO - Estimating log price statistics: phi shape=(401, 16), psi shape=(401, 1), tt_pred shape=(16, 6)
2025-07-26 09:06:13,492 - core.stats - INFO - Estimated log price predictions: shape=(401, 6), std shape=(401, 6)
2025-07-26 09:06:13,492 - core.stats - INFO - Estimated price predictions: shape=(401, 6), std shape=(401, 6)
2025-07-26 09:06:13,493 - stocks.trend - INFO - 📊 Trend Score Distribution Analysis:
2025-07-26 09:06:13,493 - stocks.trend - INFO -   Min: -7.365, Max: 3.357
2025-07-26 09:06:13,493 - stocks.trend - INFO -   Mean: -2.995, Std: 1.500
2025-07-26 09:06:13,493 - stocks.trend - INFO -    1th percentile: -5.563
2025-07-26 09:06:13,493 - stocks.trend - INFO -    5th percentile: -4.608
2025-07-26 09:06:13,493 - stocks.trend - INFO -   10th percentile: -4.294
2025-07-26 09:06:13,493 - stocks.trend - INFO -   25th percentile: -3.901
2025-07-26 09:06:13,493 - stocks.trend - INFO -   50th percentile: -3.412
2025-07-26 09:06:13,493 - stocks.trend - INFO -   75th percentile: -2.529
2025-07-26 09:06:13,493 - stocks.trend - INFO -   90th percentile: -0.939
2025-07-26 09:06:13,493 - stocks.trend - INFO -   95th percentile: -0.206
2025-07-26 09:06:13,493 - stocks.trend - INFO -   99th percentile:  2.190
2025-07-26 09:06:13,493 - stocks.trend - INFO - 📈 Stock Count by Range:
2025-07-26 09:06:13,493 - stocks.trend - INFO -   < -5.0      :   10 stocks (  2.5%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   -5.0 to -4.0:   72 stocks ( 18.0%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   -4.0 to -3.0:  175 stocks ( 43.6%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   -3.0 to -2.5:   45 stocks ( 11.2%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   -2.5 to -2.0:   16 stocks (  4.0%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   -2.0 to -1.0:   37 stocks (  9.2%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   -1.0 to 0.0 :   29 stocks (  7.2%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   0.0 to 1.0  :    7 stocks (  1.7%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   1.0 to 2.0  :    4 stocks (  1.0%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   2.0 to 2.5  :    3 stocks (  0.7%)
2025-07-26 09:06:13,493 - stocks.trend - INFO -   2.5 to 3.0  :    1 stocks (  0.2%)
2025-07-26 09:06:13,494 - stocks.trend - INFO -   3.0 to 4.0  :    2 stocks (  0.5%)
2025-07-26 09:06:13,494 - stocks.trend - INFO -   4.0 to 5.0  :    0 stocks (  0.0%)
2025-07-26 09:06:13,494 - stocks.trend - INFO -   > 5.0       :    0 stocks (  0.0%)
2025-07-26 09:06:13,494 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,494 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,494 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,494 - stocks.trend - INFO - Using 'high' extreme level with bounds: {'EXTREME BELOW TREND': np.float64(-10.562566813052541), 'HIGHLY BELOW TREND': np.float64(-5.56256681305254), 'BELOW TREND': np.float64(-4.608130895087363), 'ALONG TREND': np.float64(-0.9385354676859865), 'ABOVE TREND': np.float64(-0.20629725935553467), 'HIGHLY ABOVE TREND': np.float64(2.1895185570491664), 'EXTREME ABOVE TREND': inf}
2025-07-26 09:06:13,494 - stocks.trend - INFO - 🔍 Generating ML correlation matches for options trading...
2025-07-26 09:06:13,497 - core.stats - INFO - Estimated matches for 401 stocks
2025-07-26 09:06:13,497 - stocks.trend - INFO - ✅ Generated 401 ML correlation matches
2025-07-26 09:06:13,500 - stocks.trend - INFO - Model analysis completed successfully
2025-07-26 09:06:13,500 - stocks.trend - INFO - Stocks above trend: 40
2025-07-26 09:06:13,501 - stocks.trend - INFO - Stocks below trend: 20
2025-07-26 09:06:13,501 - stocks.trend - INFO - Stocks along trend: 341
2025-07-26 09:06:13,502 - stocks.trend - INFO - Step 3: Performing correlation analysis...
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 0 stocks EXTREME BELOW trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 4 stocks HIGHLY BELOW trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 16 stocks BELOW trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 341 stocks ALONG trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 20 stocks ABOVE trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 16 stocks HIGHLY ABOVE trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Found 4 stocks EXTREME ABOVE trend
2025-07-26 09:06:13,502 - stocks.trend - INFO - Identified 0 correlation opportunities
2025-07-26 09:06:13,502 - stocks.trend - INFO - Step 4: Executing trades based on correlation analysis...
2025-07-26 09:06:13,502 - stocks.trend - INFO - ✅ Using existing IBKR connection for trading
2025-07-26 09:06:13,502 - stocks.trend - INFO - Betty bot prefers strong stocks - prioritizing EXTREME ABOVE TREND candidates
2025-07-26 09:06:13,502 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,502 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,502 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,502 - stocks.trend - INFO - 🔥 ULTRA RARE: ONMD (score: 3.36)
2025-07-26 09:06:13,503 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,503 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,503 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,503 - stocks.trend - INFO - 🔥 ULTRA RARE: OPI (score: 3.08)
2025-07-26 09:06:13,503 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,503 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,503 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,503 - stocks.trend - INFO - 🔥 ULTRA RARE: ZCMD (score: 2.96)
2025-07-26 09:06:13,504 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,504 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,504 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,504 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,504 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,504 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,504 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,504 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,504 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,506 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,506 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,506 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,506 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,506 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,506 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,507 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,507 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,507 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,508 - stocks.trend - INFO - 📊 Dynamic thresholds based on current data:
2025-07-26 09:06:13,508 - stocks.trend - INFO -    1st percentile: -5.6, 5th: -4.6, 10th: -4.3
2025-07-26 09:06:13,508 - stocks.trend - INFO -    90th percentile: -0.9, 95th: -0.2, 99th: 2.2
2025-07-26 09:06:13,508 - stocks.trend - INFO - Executing trades for 10 stocks...
2025-07-26 09:06:13,509 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 19291...
2025-07-26 09:06:13,509 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 19291...
2025-07-26 09:06:13,510 - ib_insync.client - INFO - Connected
2025-07-26 09:06:13,519 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:06:13,520 - ib_insync.client - INFO - API connection ready
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:06:13,520 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:06:13,824 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:06:13,824 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 19291
2025-07-26 09:06:15,897 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:06:15,898 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:06:15,898 - stocks.trend - INFO - ✅ Bot connected to IBKR for trade execution
2025-07-26 09:06:15,950 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='TRIB', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='TRIB', tradingClass='NMS'), order=MarketOrder(orderId=5, clientId=19291, action='BUY', totalQuantity=1486), orderStatus=OrderStatus(orderId=5, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 15, 950099, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:15,951 - IBKRClient - INFO - Placed BUY market order for 1486 shares of TRIB
2025-07-26 09:06:16,499 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='DHAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='DHAI', tradingClass='NMS'), order=MarketOrder(orderId=7, clientId=19291, action='BUY', totalQuantity=3724), orderStatus=OrderStatus(orderId=7, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 16, 499014, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:16,499 - IBKRClient - INFO - Placed BUY market order for 3724 shares of DHAI
2025-07-26 09:06:17,049 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=766915749, symbol='MNDR', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='MNDR', tradingClass='SCM'), order=MarketOrder(orderId=9, clientId=19291, action='BUY', totalQuantity=934), orderStatus=OrderStatus(orderId=9, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 17, 48621, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:17,049 - IBKRClient - INFO - Placed BUY market order for 934 shares of MNDR
2025-07-26 09:06:17,597 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=525260348, symbol='SWAG', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='SWAG', tradingClass='SCM'), order=MarketOrder(orderId=11, clientId=19291, action='BUY', totalQuantity=675), orderStatus=OrderStatus(orderId=11, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 17, 597639, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:17,597 - IBKRClient - INFO - Placed BUY market order for 675 shares of SWAG
2025-07-26 09:06:18,147 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=542699775, symbol='ZTEK', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZTEK', tradingClass='SCM'), order=MarketOrder(orderId=13, clientId=19291, action='BUY', totalQuantity=1123), orderStatus=OrderStatus(orderId=13, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 18, 147207, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:18,147 - IBKRClient - INFO - Placed BUY market order for 1123 shares of ZTEK
2025-07-26 09:06:18,698 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=628816786, symbol='PSTV', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='PSTV', tradingClass='SCM'), order=MarketOrder(orderId=15, clientId=19291, action='BUY', totalQuantity=1887), orderStatus=OrderStatus(orderId=15, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 18, 698469, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:18,699 - IBKRClient - INFO - Placed BUY market order for 1887 shares of PSTV
2025-07-26 09:06:19,248 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=511470153, symbol='ZBAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZBAI', tradingClass='SCM'), order=MarketOrder(orderId=17, clientId=19291, action='BUY', totalQuantity=2376), orderStatus=OrderStatus(orderId=17, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 19, 248607, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:19,249 - IBKRClient - INFO - Placed BUY market order for 2376 shares of ZBAI
2025-07-26 09:06:19,751 - stocks.trend - INFO - Trading completed:
2025-07-26 09:06:19,751 - stocks.trend - INFO - Portfolio value: $30000.00
2025-07-26 09:06:19,751 - stocks.trend - INFO - Cash: $23003.46
2025-07-26 09:06:19,752 - stocks.trend - INFO - Invested: $6996.54
2025-07-26 09:06:19,752 - stocks.trend - INFO - Positions: 7
2025-07-26 09:06:19,752 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 712 B sent in 9 messages, 34.2 kB received in 744 messages, session time 14.0 s.
2025-07-26 09:06:19,752 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:06:19,753 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:06:19,753 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 2.50 kB sent in 23 messages, 38.6 kB received in 667 messages, session time 6.24 s.
2025-07-26 09:06:19,753 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:06:19,754 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:06:19,754 - stocks.trend - INFO - 🔍 Checking options arbitrage: enable_options=False
2025-07-26 09:06:19,754 - stocks.trend - INFO - ❌ Options arbitrage is DISABLED - skipping Step 5
2025-07-26 09:06:19,754 - stocks.trend - INFO - Step 6: Managing portfolio risk with smart stop losses...
2025-07-26 09:06:19,778 - stocks.portfolio - INFO - 🔄 Starting risk management cycle...
2025-07-26 09:06:19,780 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 982...
2025-07-26 09:06:19,780 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 982...
2025-07-26 09:06:19,780 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:06:19,781 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:06:19,781 - ib_insync.client - INFO - Connected
2025-07-26 09:06:19,793 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:06:19,794 - ib_insync.client - INFO - API connection ready
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:06:19,794 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:06:20,198 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:06:20,198 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 982
2025-07-26 09:06:22,261 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:06:22,262 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:06:22,262 - IBKRClient - INFO - Connecting to 127.0.0.1:7497 with clientId 500...
2025-07-26 09:06:22,262 - ib_insync.client - INFO - Connecting to 127.0.0.1:7497 with clientId 500...
2025-07-26 09:06:22,263 - ib_insync.client - INFO - Connected
2025-07-26 09:06:22,268 - ib_insync.client - INFO - Logged on to server version 176
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm.nj
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:hfarm
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2103, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u4e2d\u65ad:usfuture
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usopt.nj
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:cashfarm
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2104, reqId -1: \u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:usfarm
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:apachmds
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2106, reqId -1: \u5386\u53f2\u5e02\u573a\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:ushmds
2025-07-26 09:06:22,269 - ib_insync.wrapper - INFO - Warning 2158, reqId -1: Sec-def\u6570\u636e\u573a\u8fde\u63a5\u6b63\u5e38:secdefhk
2025-07-26 09:06:22,269 - ib_insync.client - INFO - API connection ready
2025-07-26 09:06:22,671 - ib_insync.ib - INFO - Synchronization complete
2025-07-26 09:06:22,671 - IBKRClient - INFO - ✅ Successfully connected to IBKR at 127.0.0.1:7497 with clientId 500
2025-07-26 09:06:24,727 - IBKRClient - INFO - Account type: INDIVIDUAL
2025-07-26 09:06:24,727 - IBKRClient - INFO - Managed accounts: ['DUK362830']
2025-07-26 09:06:24,727 - stocks.portfolio - INFO - 🛡️ Starting portfolio risk management...
2025-07-26 09:06:24,728 - stocks.portfolio - INFO - Managing 7 positions
2025-07-26 09:06:24,729 - stocks.risk - INFO - Stop loss calculation for TRIB:
2025-07-26 09:06:24,729 - stocks.risk - INFO -   Base stop: $0.57, Adjusted: $0.64
2025-07-26 09:06:24,729 - stocks.risk - INFO -   Base profit: $0.84, Adjusted: $0.89
2025-07-26 09:06:24,729 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:24,792 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='TRIB', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='TRIB', tradingClass='NMS'), order=StopOrder(orderId=39, clientId=500, action='SELL', totalQuantity=1486, auxPrice=np.float64(0.****************)), orderStatus=OrderStatus(orderId=39, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 24, 791856, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:24,792 - IBKRClient - INFO - Placed SELL stop loss order for 1486 shares of TRIB at $0.****************
2025-07-26 09:06:24,792 - stocks.risk - INFO - ✅ Stop loss order placed for TRIB at $0.64
2025-07-26 09:06:24,792 - stocks.risk - INFO - Stop loss calculation for DHAI:
2025-07-26 09:06:24,792 - stocks.risk - INFO -   Base stop: $0.23, Adjusted: $0.26
2025-07-26 09:06:24,793 - stocks.risk - INFO -   Base profit: $0.34, Adjusted: $0.36
2025-07-26 09:06:24,793 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:24,795 - ib_insync.wrapper - INFO - Warning 110, reqId 39: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:06:24,940 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=*********, symbol='DHAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='DHAI', tradingClass='NMS'), order=StopOrder(orderId=41, clientId=500, action='SELL', totalQuantity=3724, auxPrice=np.float64(0.*****************)), orderStatus=OrderStatus(orderId=41, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 24, 939862, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:24,940 - IBKRClient - INFO - Placed SELL stop loss order for 3724 shares of DHAI at $0.*****************
2025-07-26 09:06:24,940 - stocks.risk - INFO - ✅ Stop loss order placed for DHAI at $0.26
2025-07-26 09:06:24,940 - stocks.risk - INFO - Stop loss calculation for MNDR:
2025-07-26 09:06:24,940 - stocks.risk - INFO -   Base stop: $0.91, Adjusted: $0.96
2025-07-26 09:06:24,940 - stocks.risk - INFO -   Base profit: $1.34, Adjusted: $1.38
2025-07-26 09:06:24,940 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:24,941 - ib_insync.wrapper - INFO - Warning 110, reqId 41: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:06:25,089 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=766915749, symbol='MNDR', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='MNDR', tradingClass='SCM'), order=StopOrder(orderId=43, clientId=500, action='SELL', totalQuantity=934, auxPrice=np.float64(0.96407)), orderStatus=OrderStatus(orderId=43, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 25, 89149, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:25,089 - IBKRClient - INFO - Placed SELL stop loss order for 934 shares of MNDR at $0.96407
2025-07-26 09:06:25,089 - stocks.risk - INFO - ✅ Stop loss order placed for MNDR at $0.96
2025-07-26 09:06:25,089 - stocks.risk - INFO - Stop loss calculation for SWAG:
2025-07-26 09:06:25,089 - stocks.risk - INFO -   Base stop: $1.26, Adjusted: $1.40
2025-07-26 09:06:25,089 - stocks.risk - INFO -   Base profit: $1.85, Adjusted: $1.95
2025-07-26 09:06:25,090 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:06:25,090 - ib_insync.wrapper - INFO - Warning 110, reqId 43: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:06:25,135 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=525260348, symbol='SWAG', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='SWAG', tradingClass='SCM'), order=StopOrder(orderId=45, clientId=500, action='SELL', totalQuantity=675, auxPrice=np.float64(1.3963800000000002)), orderStatus=OrderStatus(orderId=45, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 25, 135398, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:25,136 - IBKRClient - INFO - Placed SELL stop loss order for 675 shares of SWAG at $1.3963800000000002
2025-07-26 09:06:25,136 - stocks.risk - INFO - ✅ Stop loss order placed for SWAG at $1.40
2025-07-26 09:06:25,136 - stocks.risk - INFO - Stop loss calculation for ZTEK:
2025-07-26 09:06:25,136 - stocks.risk - INFO -   Base stop: $0.76, Adjusted: $0.84
2025-07-26 09:06:25,136 - stocks.risk - INFO -   Base profit: $1.11, Adjusted: $1.17
2025-07-26 09:06:25,137 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:06:25,137 - ib_insync.wrapper - INFO - Warning 110, reqId 45: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:06:25,183 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=542699775, symbol='ZTEK', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZTEK', tradingClass='SCM'), order=StopOrder(orderId=47, clientId=500, action='SELL', totalQuantity=1123, auxPrice=np.float64(0.839715)), orderStatus=OrderStatus(orderId=47, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 25, 183284, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:25,184 - IBKRClient - INFO - Placed SELL stop loss order for 1123 shares of ZTEK at $0.839715
2025-07-26 09:06:25,184 - stocks.risk - INFO - ✅ Stop loss order placed for ZTEK at $0.84
2025-07-26 09:06:25,184 - stocks.risk - INFO - Stop loss calculation for PSTV:
2025-07-26 09:06:25,184 - stocks.risk - INFO -   Base stop: $0.45, Adjusted: $0.48
2025-07-26 09:06:25,184 - stocks.risk - INFO -   Base profit: $0.66, Adjusted: $0.68
2025-07-26 09:06:25,185 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:25,185 - ib_insync.wrapper - INFO - Warning 110, reqId 47: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:06:25,233 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=628816786, symbol='PSTV', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='PSTV', tradingClass='SCM'), order=StopOrder(orderId=49, clientId=500, action='SELL', totalQuantity=1887, auxPrice=np.float64(0.47734980000000005)), orderStatus=OrderStatus(orderId=49, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 25, 233377, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:25,234 - IBKRClient - INFO - Placed SELL stop loss order for 1887 shares of PSTV at $0.47734980000000005
2025-07-26 09:06:25,234 - stocks.risk - INFO - ✅ Stop loss order placed for PSTV at $0.48
2025-07-26 09:06:25,234 - stocks.risk - INFO - Stop loss calculation for ZBAI:
2025-07-26 09:06:25,234 - stocks.risk - INFO -   Base stop: $0.36, Adjusted: $0.38
2025-07-26 09:06:25,234 - stocks.risk - INFO -   Base profit: $0.53, Adjusted: $0.54
2025-07-26 09:06:25,234 - stocks.risk - INFO -   Adjustments - Trend: 0.000, Vol: 0.250, Corr: 0.000
2025-07-26 09:06:25,235 - ib_insync.wrapper - INFO - Warning 110, reqId 49: \u4ef7\u683c\u4e0d\u7b26\u5408\u8be5\u5408\u7ea6\u7684\u6700\u5c0f\u4ef7\u683c\u53d8\u52a8\u8981\u6c42\u3002
2025-07-26 09:06:25,285 - ib_insync.ib - INFO - placeOrder: New order Trade(contract=Stock(conId=511470153, symbol='ZBAI', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='ZBAI', tradingClass='SCM'), order=StopOrder(orderId=51, clientId=500, action='SELL', totalQuantity=2376, auxPrice=np.float64(0.3790507)), orderStatus=OrderStatus(orderId=51, status='PendingSubmit', filled=0.0, remaining=0.0, avgFillPrice=0.0, permId=0, parentId=0, lastFillPrice=0.0, clientId=0, whyHeld='', mktCapPrice=0.0), fills=[], log=[TradeLogEntry(time=datetime.datetime(2025, 7, 26, 1, 6, 25, 285096, tzinfo=datetime.timezone.utc), status='PendingSubmit', message='', errorCode=0)], advancedError='')
2025-07-26 09:06:25,286 - IBKRClient - INFO - Placed SELL stop loss order for 2376 shares of ZBAI at $0.3790507
2025-07-26 09:06:25,286 - stocks.risk - INFO - ✅ Stop loss order placed for ZBAI at $0.38
2025-07-26 09:06:25,286 - stocks.risk - INFO - Stop loss calculation for TRIB:
2025-07-26 09:06:25,286 - stocks.risk - INFO -   Base stop: $0.57, Adjusted: $0.64
2025-07-26 09:06:25,289 - stocks.risk - INFO -   Base profit: $0.84, Adjusted: $0.89
2025-07-26 09:06:25,289 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:25,289 - stocks.risk - INFO - Stop loss calculation for DHAI:
2025-07-26 09:06:25,290 - stocks.risk - INFO -   Base stop: $0.23, Adjusted: $0.26
2025-07-26 09:06:25,290 - stocks.risk - INFO -   Base profit: $0.34, Adjusted: $0.36
2025-07-26 09:06:25,290 - stocks.risk - INFO -   Adjustments - Trend: 0.300, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:25,292 - stocks.risk - INFO - Stop loss calculation for MNDR:
2025-07-26 09:06:25,292 - stocks.risk - INFO -   Base stop: $0.91, Adjusted: $0.96
2025-07-26 09:06:25,292 - stocks.risk - INFO -   Base profit: $1.34, Adjusted: $1.38
2025-07-26 09:06:25,292 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:25,293 - stocks.risk - INFO - Stop loss calculation for SWAG:
2025-07-26 09:06:25,293 - stocks.risk - INFO -   Base stop: $1.26, Adjusted: $1.40
2025-07-26 09:06:25,293 - stocks.risk - INFO -   Base profit: $1.85, Adjusted: $1.95
2025-07-26 09:06:25,293 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:06:25,293 - stocks.risk - INFO - Stop loss calculation for ZTEK:
2025-07-26 09:06:25,293 - stocks.risk - INFO -   Base stop: $0.76, Adjusted: $0.84
2025-07-26 09:06:25,294 - stocks.risk - INFO -   Base profit: $1.11, Adjusted: $1.17
2025-07-26 09:06:25,296 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: 0.000
2025-07-26 09:06:25,297 - stocks.risk - INFO - Stop loss calculation for PSTV:
2025-07-26 09:06:25,297 - stocks.risk - INFO -   Base stop: $0.45, Adjusted: $0.48
2025-07-26 09:06:25,297 - stocks.risk - INFO -   Base profit: $0.66, Adjusted: $0.68
2025-07-26 09:06:25,297 - stocks.risk - INFO -   Adjustments - Trend: 0.150, Vol: 0.250, Corr: -0.200
2025-07-26 09:06:25,298 - stocks.risk - INFO - Stop loss calculation for ZBAI:
2025-07-26 09:06:25,300 - stocks.risk - INFO -   Base stop: $0.36, Adjusted: $0.38
2025-07-26 09:06:25,300 - stocks.risk - INFO -   Base profit: $0.53, Adjusted: $0.54
2025-07-26 09:06:25,300 - stocks.risk - INFO -   Adjustments - Trend: 0.000, Vol: 0.250, Corr: 0.000
2025-07-26 09:06:25,301 - stocks.portfolio - INFO - ✅ Portfolio risk management completed
2025-07-26 09:06:25,301 - stocks.portfolio - INFO - 📊 Portfolio Summary:
2025-07-26 09:06:25,301 - stocks.portfolio - INFO -   Total Value: $6,996.54
2025-07-26 09:06:25,301 - stocks.portfolio - INFO -   Total P&L: $0.00 (0.0%)
2025-07-26 09:06:25,301 - stocks.portfolio - INFO -   Risk Level: LOW
2025-07-26 09:06:25,301 - stocks.portfolio - INFO -   Stop Loss Coverage: 100.0%
2025-07-26 09:06:25,302 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 712 B sent in 9 messages, 34.2 kB received in 744 messages, session time 5.52 s.
2025-07-26 09:06:25,302 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:06:25,302 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:06:25,302 - ib_insync.ib - INFO - Disconnecting from 127.0.0.1:7497, 2.61 kB sent in 23 messages, 39.8 kB received in 673 messages, session time 3.04 s.
2025-07-26 09:06:25,302 - ib_insync.client - INFO - Disconnecting
2025-07-26 09:06:25,302 - IBKRClient - INFO - Disconnected from IBKR
2025-07-26 09:06:25,302 - stocks.trend - INFO - ✅ Portfolio risk management completed successfully
2025-07-26 09:06:25,302 - stocks.trend - INFO - 📊 Portfolio Value: $6,996.54
2025-07-26 09:06:25,302 - stocks.trend - INFO - 💰 Total P&L: $0.00 (0.0%)
2025-07-26 09:06:25,303 - stocks.trend - INFO - 🛡️ Risk Level: LOW
2025-07-26 09:06:25,303 - stocks.trend - INFO - 📋 Stop Loss Orders: 7
2025-07-26 09:06:25,303 - stocks.trend - INFO - 💡 Risk Management Recommendations:
2025-07-26 09:06:25,303 - stocks.trend - INFO -   ✅ Portfolio risk appears well managed
2025-07-26 09:06:25,324 - stocks.trend - INFO - Loaded classification info for 457 stocks from stock_info.csv
2025-07-26 09:06:25,340 - stocks.trend - INFO - 📊 Daily results saved to results/20250726/daily_results_20250726.csv
2025-07-26 09:06:25,340 - stocks.trend - INFO - ============================================================
2025-07-26 09:06:25,340 - stocks.trend - INFO - Daily trading analysis completed successfully!
2025-07-26 09:06:25,341 - __main__ - INFO - ✅ 交易分析成功完成
2025-07-26 09:06:25,344 - __main__ - INFO - ============================================================
2025-07-26 09:06:25,344 - __main__ - INFO - 🎉 完整交易工作流成功完成！
2025-07-26 09:06:25,344 - __main__ - INFO - ⏱️ 总时间: 00:00:23
2025-07-26 09:06:25,344 - __main__ - INFO - 📊 结束时间: 2025-07-26 09:06:25
2025-07-26 09:06:25,344 - __main__ - INFO - ============================================================
2025-07-26 09:06:25,344 - ib_insync.client - INFO - Disconnected.
2025-07-26 09:06:25,344 - ib_insync.client - INFO - Disconnected.
