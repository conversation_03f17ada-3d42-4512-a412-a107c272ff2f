#!/usr/bin/env python
"""
期权出场策略配置
Options Exit Strategy Configuration

定义期权交易的出场条件、止盈止损规则和风险管理参数
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from enum import Enum

class ExitReason(Enum):
    """出场原因枚举"""
    TAKE_PROFIT_25PCT = "TAKE_PROFIT_25PCT"      # 25%止盈
    TAKE_PROFIT_50PCT = "TAKE_PROFIT_50PCT"      # 50%止盈
    TAKE_PROFIT_75PCT = "TAKE_PROFIT_75PCT"      # 75%止盈
    STOP_LOSS_25PCT = "STOP_LOSS_25PCT"          # 25%止损
    STOP_LOSS_50PCT = "STOP_LOSS_50PCT"          # 50%止损
    STOP_LOSS_75PCT = "STOP_LOSS_75PCT"          # 75%止损
    TIME_DECAY_EXIT = "TIME_DECAY_EXIT"          # 时间价值衰减出场
    DELTA_RISK_EXIT = "DELTA_RISK_EXIT"          # Delta风险出场
    IV_COLLAPSE_EXIT = "IV_COLLAPSE_EXIT"        # 隐含波动率崩塌出场
    CORRELATION_BREAKDOWN = "CORRELATION_BREAKDOWN"  # 相关性破裂出场
    MARKET_VOLATILITY_EXIT = "MARKET_VOLATILITY_EXIT"  # 市场波动率异常出场

class ExitUrgency(Enum):
    """出场紧急程度"""
    LOW = "LOW"          # 低紧急程度
    MEDIUM = "MEDIUM"    # 中等紧急程度
    HIGH = "HIGH"        # 高紧急程度
    CRITICAL = "CRITICAL"  # 紧急出场

@dataclass
class ExitCondition:
    """出场条件定义"""
    reason: ExitReason
    threshold: float
    urgency: ExitUrgency
    description: str
    auto_execute: bool = False  # 是否自动执行

@dataclass
class OptionsExitConfig:
    """期权出场策略配置"""
    
    # === 基础止盈止损设置 ===
    profit_taking_levels: Dict[str, float] = None
    stop_loss_levels: Dict[str, float] = None
    
    # === 时间管理 ===
    time_decay_exit_days: int = 7           # 距离到期7天内出场
    weekend_exit_threshold: int = 3         # 周末前3天考虑出场
    
    # === 希腊字母风险管理 ===
    max_delta_exposure: float = 0.90        # 最大Delta暴露
    min_delta_exposure: float = 0.10        # 最小Delta暴露
    max_theta_decay_daily: float = 0.10     # 每日最大Theta衰减
    max_vega_risk: float = 0.50             # 最大Vega风险
    
    # === 隐含波动率管理 ===
    iv_collapse_threshold: float = 0.30     # IV下降30%触发出场
    min_iv_threshold: float = 0.15          # 最低IV阈值
    iv_rank_exit_threshold: float = 0.20    # IV排名低于20%出场
    
    # === 相关性监控 ===
    correlation_breakdown_threshold: float = 0.30  # 相关性低于0.3触发出场
    correlation_check_period: int = 5       # 相关性检查周期（天）
    
    # === 市场条件出场 ===
    market_volatility_threshold: float = 0.40  # 市场波动率超过40%出场
    max_market_drawdown: float = 0.05       # 市场回撤超过5%考虑出场
    
    # === 自动执行设置 ===
    auto_execute_high_urgency: bool = True   # 自动执行高紧急程度出场
    auto_execute_critical: bool = True       # 自动执行紧急出场
    require_confirmation_medium: bool = True  # 中等紧急程度需要确认
    
    # === 出场订单设置 ===
    exit_order_type: str = "LMT"            # 出场订单类型：LMT/MKT
    limit_price_buffer: float = 0.02        # 限价单价格缓冲2%
    max_exit_attempts: int = 3               # 最大出场尝试次数
    exit_timeout_seconds: int = 300          # 出场超时时间（秒）

    def __post_init__(self):
        """初始化默认值"""
        if self.profit_taking_levels is None:
            self.profit_taking_levels = {
                "conservative": 0.25,    # 保守：25%止盈
                "moderate": 0.50,        # 适中：50%止盈
                "aggressive": 0.75       # 激进：75%止盈
            }
        
        if self.stop_loss_levels is None:
            self.stop_loss_levels = {
                "tight": 0.25,           # 紧密：25%止损
                "moderate": 0.50,        # 适中：50%止损
                "loose": 0.75            # 宽松：75%止损
            }

# 默认配置实例
DEFAULT_EXIT_CONFIG = OptionsExitConfig()

# 预定义出场条件
DEFAULT_EXIT_CONDITIONS = [
    # 止盈条件
    ExitCondition(
        reason=ExitReason.TAKE_PROFIT_25PCT,
        threshold=0.25,
        urgency=ExitUrgency.MEDIUM,
        description="25%止盈出场",
        auto_execute=False
    ),
    ExitCondition(
        reason=ExitReason.TAKE_PROFIT_50PCT,
        threshold=0.50,
        urgency=ExitUrgency.MEDIUM,
        description="50%止盈出场",
        auto_execute=True
    ),
    ExitCondition(
        reason=ExitReason.TAKE_PROFIT_75PCT,
        threshold=0.75,
        urgency=ExitUrgency.HIGH,
        description="75%止盈出场",
        auto_execute=True
    ),
    
    # 止损条件
    ExitCondition(
        reason=ExitReason.STOP_LOSS_25PCT,
        threshold=-0.25,
        urgency=ExitUrgency.MEDIUM,
        description="25%止损出场",
        auto_execute=False
    ),
    ExitCondition(
        reason=ExitReason.STOP_LOSS_50PCT,
        threshold=-0.50,
        urgency=ExitUrgency.HIGH,
        description="50%止损出场",
        auto_execute=True
    ),
    ExitCondition(
        reason=ExitReason.STOP_LOSS_75PCT,
        threshold=-0.75,
        urgency=ExitUrgency.CRITICAL,
        description="75%止损出场",
        auto_execute=True
    ),
    
    # 时间和风险条件
    ExitCondition(
        reason=ExitReason.TIME_DECAY_EXIT,
        threshold=7,  # 7天
        urgency=ExitUrgency.HIGH,
        description="时间价值衰减出场",
        auto_execute=True
    ),
    ExitCondition(
        reason=ExitReason.DELTA_RISK_EXIT,
        threshold=0.90,  # Delta > 0.90
        urgency=ExitUrgency.MEDIUM,
        description="Delta风险出场",
        auto_execute=False
    ),
    ExitCondition(
        reason=ExitReason.IV_COLLAPSE_EXIT,
        threshold=0.15,  # IV < 15%
        urgency=ExitUrgency.LOW,
        description="隐含波动率崩塌出场",
        auto_execute=False
    ),
    ExitCondition(
        reason=ExitReason.CORRELATION_BREAKDOWN,
        threshold=0.30,  # 相关性 < 0.30
        urgency=ExitUrgency.MEDIUM,
        description="相关性破裂出场",
        auto_execute=False
    )
]

def get_exit_conditions_by_urgency(urgency: ExitUrgency) -> List[ExitCondition]:
    """根据紧急程度获取出场条件"""
    return [condition for condition in DEFAULT_EXIT_CONDITIONS if condition.urgency == urgency]

def get_auto_execute_conditions() -> List[ExitCondition]:
    """获取自动执行的出场条件"""
    return [condition for condition in DEFAULT_EXIT_CONDITIONS if condition.auto_execute]

def get_exit_config_summary() -> str:
    """获取出场配置摘要"""
    config = DEFAULT_EXIT_CONFIG
    
    summary = f"""
🚪 期权出场策略配置摘要
{'='*50}

📈 止盈设置:
  保守止盈: {config.profit_taking_levels['conservative']*100:.0f}%
  适中止盈: {config.profit_taking_levels['moderate']*100:.0f}%
  激进止盈: {config.profit_taking_levels['aggressive']*100:.0f}%

📉 止损设置:
  紧密止损: {config.stop_loss_levels['tight']*100:.0f}%
  适中止损: {config.stop_loss_levels['moderate']*100:.0f}%
  宽松止损: {config.stop_loss_levels['loose']*100:.0f}%

⏰ 时间管理:
  时间衰减出场: {config.time_decay_exit_days}天
  周末出场阈值: {config.weekend_exit_threshold}天

🔢 希腊字母风险:
  最大Delta暴露: {config.max_delta_exposure:.2f}
  最小Delta暴露: {config.min_delta_exposure:.2f}
  每日最大Theta衰减: {config.max_theta_decay_daily:.2f}

📊 波动率管理:
  IV崩塌阈值: {config.iv_collapse_threshold:.2f}
  最低IV阈值: {config.min_iv_threshold:.2f}

🔗 相关性监控:
  相关性破裂阈值: {config.correlation_breakdown_threshold:.2f}
  检查周期: {config.correlation_check_period}天

🤖 自动执行:
  高紧急程度: {'启用' if config.auto_execute_high_urgency else '禁用'}
  紧急出场: {'启用' if config.auto_execute_critical else '禁用'}
  中等紧急确认: {'需要' if config.require_confirmation_medium else '不需要'}

📋 出场条件总数: {len(DEFAULT_EXIT_CONDITIONS)}
🤖 自动执行条件: {len(get_auto_execute_conditions())}
{'='*50}
"""
    return summary

if __name__ == "__main__":
    # 显示配置摘要
    print(get_exit_config_summary())
    
    # 显示所有出场条件
    print("\n📋 所有出场条件:")
    print("="*60)
    for i, condition in enumerate(DEFAULT_EXIT_CONDITIONS, 1):
        auto_flag = "🤖" if condition.auto_execute else "👤"
        print(f"{i:2d}. {auto_flag} {condition.description}")
        print(f"     原因: {condition.reason.value}")
        print(f"     阈值: {condition.threshold}")
        print(f"     紧急程度: {condition.urgency.value}")
        print()
