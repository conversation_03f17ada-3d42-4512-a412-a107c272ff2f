#!/usr/bin/env python
"""
期权仓位监控脚本
Options Position Monitoring Script

实时监控期权仓位的盈亏、风险指标和出场信号
Real-time monitoring of options positions P&L, risk metrics and exit signals
"""

import asyncio
import argparse
import logging
import json
import os
from datetime import datetime
from typing import Dict, List

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'results/{datetime.now().strftime("%Y%m%d")}/logs/options_monitor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

async def monitor_options_positions(paper_trading: bool = True, auto_exit: bool = False):
    """监控期权仓位"""
    try:
        from options.tracker import OptionsPerformanceMonitor
        from core.config import DEFAULT_CONFIG
        
        # 设置IBKR配置
        ibkr_config = DEFAULT_CONFIG
        ibkr_config.paper_trading = paper_trading
        ibkr_config.port = 7497 if paper_trading else 7496
        
        logger.info("🎯 期权仓位监控系统启动")
        logger.info("="*60)
        logger.info(f"📊 交易模式: {'纸上交易' if paper_trading else '实盘交易'}")
        logger.info(f"🔌 IBKR端口: {ibkr_config.port}")
        logger.info(f"🤖 自动出场: {'启用' if auto_exit else '禁用'}")
        logger.info("="*60)
        
        # 初始化监控器
        monitor = OptionsPerformanceMonitor()
        
        # 获取当前仓位
        logger.info("📊 正在获取当前期权仓位...")
        current_positions = await monitor.get_current_positions_from_ibkr(ibkr_config)
        
        if not current_positions:
            logger.info("📊 当前无期权仓位")
            return True
        
        logger.info(f"📊 发现 {len(current_positions)} 个期权仓位")
        
        # 更新仓位价格
        logger.info("💰 正在更新仓位价格...")
        await monitor.update_all_positions_from_ibkr(ibkr_config)
        
        # 获取性能报告
        performance_report = monitor.get_current_performance()
        
        # 显示仓位详情
        print("\n" + "="*80)
        print("📊 期权仓位详情 (OPTIONS POSITIONS DETAIL)")
        print("="*80)
        
        if 'current_positions' in performance_report:
            total_pnl = 0
            total_investment = 0
            
            for i, pos in enumerate(performance_report['current_positions'], 1):
                print(f"\n🔸 仓位 {i}: {pos['symbol']} {pos['type']} ${pos['strike']:.0f} {pos['expiry']}")
                print(f"   数量: {pos['quantity']} 合约")
                print(f"   入场价: ${pos['entry_price']:.2f}")
                print(f"   当前价: ${pos['current_price']:.2f}")
                print(f"   盈亏: ${pos['pnl']:.2f} ({pos['pnl_pct']*100:+.1f}%)")
                print(f"   Delta: {pos['delta']:.3f}, Theta: {pos['theta']:.3f}")
                
                total_pnl += pos['pnl']
                total_investment += pos['entry_price'] * pos['quantity'] * 100
        
        # 显示总体指标
        if 'performance_metrics' in performance_report:
            metrics = performance_report['performance_metrics']
            print(f"\n📈 总体表现:")
            print(f"   总盈亏: ${metrics.get('total_pnl', 0):.2f}")
            print(f"   已实现盈亏: ${metrics.get('realized_pnl', 0):.2f}")
            print(f"   未实现盈亏: ${metrics.get('unrealized_pnl', 0):.2f}")
            print(f"   胜率: {metrics.get('win_rate', 0)*100:.1f}%")
            print(f"   总交易数: {metrics.get('total_trades', 0)}")
            print(f"   当前仓位: {metrics.get('current_positions', 0)}")
        
        # 显示组合希腊字母
        if 'portfolio_greeks' in performance_report:
            greeks = performance_report['portfolio_greeks']
            print(f"\n🔢 组合希腊字母:")
            print(f"   总Delta: {greeks.get('total_delta', 0):.3f}")
            print(f"   总Gamma: {greeks.get('total_gamma', 0):.3f}")
            print(f"   总Theta: {greeks.get('total_theta', 0):.3f}")
            print(f"   总Vega: {greeks.get('total_vega', 0):.3f}")
        
        # 检查出场信号
        logger.info("\n🚨 正在检查出场信号...")
        exit_signals = await monitor.check_exit_conditions()
        
        if exit_signals:
            print(f"\n🚨 发现 {len(exit_signals)} 个出场信号:")
            print("="*60)
            
            for signal in exit_signals:
                pos = signal['position']
                print(f"\n⚠️  {signal['position_id']}")
                print(f"   出场原因: {signal['exit_reason']}")
                print(f"   当前盈亏: {signal['current_pnl_pct']*100:+.1f}%")
                print(f"   建议价格: ${signal['recommended_exit_price']:.2f}")
                print(f"   紧急程度: {signal['urgency']}")
            
            # 自动出场
            if auto_exit:
                logger.info("🤖 自动出场模式启用，正在执行出场交易...")
                
                for signal in exit_signals:
                    if signal['urgency'] in ['HIGH', 'MEDIUM']:  # 只自动执行高和中等紧急程度的出场
                        logger.info(f"🚀 自动执行出场: {signal['position_id']}")
                        success = await monitor.execute_exit_trade(signal, ibkr_config)
                        
                        if success:
                            logger.info(f"✅ 自动出场成功: {signal['position_id']}")
                        else:
                            logger.error(f"❌ 自动出场失败: {signal['position_id']}")
                    else:
                        logger.info(f"⏸️  跳过低紧急程度出场: {signal['position_id']}")
            else:
                print(f"\n💡 提示: 使用 --auto-exit 参数可启用自动出场")
        else:
            print(f"\n✅ 当前无出场信号，所有仓位状态良好")
        
        # 保存监控报告
        today = datetime.now().strftime("%Y%m%d")
        report_dir = f"results/{today}"
        os.makedirs(report_dir, exist_ok=True)
        
        report_path = f"{report_dir}/options_monitor_report_{datetime.now().strftime('%H%M%S')}.json"
        with open(report_path, 'w') as f:
            json.dump(performance_report, f, indent=2, default=str)
        
        logger.info(f"\n📄 监控报告已保存: {report_path}")
        
        print("\n" + "="*80)
        print("✅ 期权监控完成")
        print("="*80)
        
        return True
        
    except Exception as e:
        logger.error(f"期权监控错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="期权仓位监控脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 监控纸上交易期权仓位
  python monitor_options.py --paper
  
  # 监控实盘期权仓位
  python monitor_options.py --live
  
  # 启用自动出场（高风险仓位）
  python monitor_options.py --paper --auto-exit
  
  # 持续监控（每5分钟检查一次）
  python monitor_options.py --paper --continuous --interval 300
        """
    )
    
    # 交易模式
    trading_group = parser.add_mutually_exclusive_group(required=True)
    trading_group.add_argument('--paper', action='store_true', 
                              help='纸上交易模式 (IBKR端口7497)')
    trading_group.add_argument('--live', action='store_true', 
                              help='实盘交易模式 (IBKR端口7496)')
    
    # 监控选项
    parser.add_argument('--auto-exit', action='store_true',
                       help='启用自动出场（高和中等风险仓位）')
    parser.add_argument('--continuous', action='store_true',
                       help='持续监控模式')
    parser.add_argument('--interval', type=int, default=300,
                       help='持续监控间隔（秒，默认300秒=5分钟）')
    
    args = parser.parse_args()
    
    # 确保结果目录存在
    today = datetime.now().strftime("%Y%m%d")
    os.makedirs(f"results/{today}/logs", exist_ok=True)
    
    try:
        if args.continuous:
            logger.info(f"🔄 启动持续监控模式，间隔 {args.interval} 秒")
            
            while True:
                success = asyncio.run(monitor_options_positions(
                    paper_trading=args.paper,
                    auto_exit=args.auto_exit
                ))
                
                if not success:
                    logger.error("监控失败，等待下次检查...")
                
                logger.info(f"⏰ 等待 {args.interval} 秒后进行下次检查...")
                import time
                time.sleep(args.interval)
        else:
            # 单次监控
            success = asyncio.run(monitor_options_positions(
                paper_trading=args.paper,
                auto_exit=args.auto_exit
            ))
            
            return 0 if success else 1
            
    except KeyboardInterrupt:
        logger.info("⏹️ 监控被用户中断")
        return 0
    except Exception as e:
        logger.error(f"💥 监控系统错误: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
