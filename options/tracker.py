#!/usr/bin/env python3
"""
期权交易性能监控系统

实时监控期权交易策略的表现，提供全面的风险预警和性能分析。
包含仓位跟踪、盈亏分析、Greeks监控、风险指标计算等功能。
支持多策略监控和自动化风险管理。
"""

import asyncio
import json
import logging
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Dict, List

import matplotlib.pyplot as plt
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class OptionsPosition:
    """期权仓位信息"""

    symbol: str
    option_type: str  # CALL/PUT
    strike: float
    expiry: str
    quantity: int
    entry_price: float
    current_price: float
    entry_time: datetime
    underlying_price: float
    implied_volatility: float
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0


@dataclass
class PerformanceMetrics:
    """性能指标"""

    total_pnl: float
    realized_pnl: float
    unrealized_pnl: float
    win_rate: float
    avg_win: float
    avg_loss: float
    max_drawdown: float
    sharpe_ratio: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    current_positions: int


class OptionsPerformanceMonitor:
    """期权性能监控器"""

    def __init__(self, config=None):
        self.config = config
        self.positions: Dict[str, OptionsPosition] = {}
        self.closed_trades: List[Dict] = []
        self.performance_history: List[Dict] = []
        self.risk_alerts: List[Dict] = []

        # 监控参数
        self.max_portfolio_delta = 100  # 最大组合Delta
        self.max_single_position_loss = 0.5  # 单仓位最大亏损50%
        self.max_portfolio_loss = 0.15  # 组合最大亏损15%

    async def add_position(self, position: OptionsPosition) -> bool:
        """添加新仓位"""
        try:
            position_id = f"{position.symbol}_{position.option_type}_{position.strike}_{position.expiry}"
            self.positions[position_id] = position

            logger.info(f"✅ Added options position: {position_id}")
            logger.info(
                f"   Entry: ${position.entry_price:.2f}, Quantity: {position.quantity}"
            )

            # 检查风险
            await self._check_position_risk(position_id)

            return True

        except Exception as e:
            logger.error(f"Error adding position: {e}")
            return False

    async def update_position_prices(self, price_updates: Dict[str, Dict]) -> bool:
        """更新仓位价格"""
        try:
            for position_id, position in self.positions.items():
                symbol = position.symbol

                if symbol in price_updates:
                    # 更新标的价格
                    position.underlying_price = price_updates[symbol].get(
                        "underlying_price", position.underlying_price
                    )

                    # 更新期权价格
                    option_key = (
                        f"{position.option_type}_{position.strike}_{position.expiry}"
                    )
                    if option_key in price_updates[symbol]:
                        option_data = price_updates[symbol][option_key]
                        position.current_price = option_data.get(
                            "price", position.current_price
                        )
                        position.implied_volatility = option_data.get(
                            "iv", position.implied_volatility
                        )
                        position.delta = option_data.get("delta", position.delta)
                        position.gamma = option_data.get("gamma", position.gamma)
                        position.theta = option_data.get("theta", position.theta)
                        position.vega = option_data.get("vega", position.vega)

            # 更新性能指标
            await self._update_performance_metrics()

            # 检查风险警报
            await self._check_risk_alerts()

            return True

        except Exception as e:
            logger.error(f"Error updating position prices: {e}")
            return False

    async def close_position(
        self, position_id: str, exit_price: float, exit_time: datetime = None
    ) -> bool:
        """平仓"""
        try:
            if position_id not in self.positions:
                logger.warning(f"Position {position_id} not found")
                return False

            position = self.positions[position_id]
            exit_time = exit_time or datetime.now()

            # 计算盈亏
            pnl = (
                (exit_price - position.entry_price) * position.quantity * 100
            )  # 期权合约乘数
            pnl_pct = (exit_price - position.entry_price) / position.entry_price

            # 记录交易
            trade_record = {
                "position_id": position_id,
                "symbol": position.symbol,
                "option_type": position.option_type,
                "strike": position.strike,
                "expiry": position.expiry,
                "quantity": position.quantity,
                "entry_price": position.entry_price,
                "exit_price": exit_price,
                "entry_time": position.entry_time,
                "exit_time": exit_time,
                "holding_days": (exit_time - position.entry_time).days,
                "pnl": pnl,
                "pnl_pct": pnl_pct,
                "underlying_entry": position.underlying_price,
                "underlying_exit": position.underlying_price,  # 需要实时更新
            }

            self.closed_trades.append(trade_record)
            del self.positions[position_id]

            logger.info(f"✅ Closed position: {position_id}")
            logger.info(f"   P&L: ${pnl:.2f} ({pnl_pct * 100:.1f}%)")

            return True

        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return False

    async def _update_performance_metrics(self) -> None:
        """更新性能指标"""
        try:
            # 计算未实现盈亏
            unrealized_pnl = 0
            for position in self.positions.values():
                position_pnl = (
                    (position.current_price - position.entry_price)
                    * position.quantity
                    * 100
                )
                unrealized_pnl += position_pnl

            # 计算已实现盈亏
            realized_pnl = sum(trade["pnl"] for trade in self.closed_trades)

            # 计算胜率
            if self.closed_trades:
                winning_trades = len([t for t in self.closed_trades if t["pnl"] > 0])
                win_rate = winning_trades / len(self.closed_trades)
                avg_win = (
                    np.mean([t["pnl"] for t in self.closed_trades if t["pnl"] > 0])
                    if winning_trades > 0
                    else 0
                )
                avg_loss = (
                    np.mean([t["pnl"] for t in self.closed_trades if t["pnl"] < 0])
                    if len(self.closed_trades) - winning_trades > 0
                    else 0
                )
            else:
                win_rate = 0
                avg_win = 0
                avg_loss = 0
                winning_trades = 0

            # 计算最大回撤
            pnl_series = [t["pnl"] for t in self.closed_trades]
            if pnl_series:
                cumulative_pnl = np.cumsum(pnl_series)
                running_max = np.maximum.accumulate(cumulative_pnl)
                drawdown = cumulative_pnl - running_max
                max_drawdown = np.min(drawdown) if len(drawdown) > 0 else 0
            else:
                max_drawdown = 0

            # 计算夏普比率（简化版）
            if pnl_series and len(pnl_series) > 1:
                returns = np.array(pnl_series)
                sharpe_ratio = (
                    np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
                )
            else:
                sharpe_ratio = 0

            # 创建性能指标
            metrics = PerformanceMetrics(
                total_pnl=realized_pnl + unrealized_pnl,
                realized_pnl=realized_pnl,
                unrealized_pnl=unrealized_pnl,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                total_trades=len(self.closed_trades),
                winning_trades=winning_trades,
                losing_trades=len(self.closed_trades) - winning_trades,
                current_positions=len(self.positions),
            )

            # 记录历史
            self.performance_history.append(
                {"timestamp": datetime.now(), "metrics": asdict(metrics)}
            )

        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")

    async def _check_position_risk(self, position_id: str) -> None:
        """检查单个仓位风险"""
        try:
            position = self.positions[position_id]

            # 检查单仓位亏损
            current_pnl_pct = (
                position.current_price - position.entry_price
            ) / position.entry_price
            if current_pnl_pct < -self.max_single_position_loss:
                alert = {
                    "type": "SINGLE_POSITION_LOSS",
                    "position_id": position_id,
                    "current_loss": current_pnl_pct,
                    "threshold": -self.max_single_position_loss,
                    "timestamp": datetime.now(),
                    "message": f"Position {position_id} loss exceeds {self.max_single_position_loss * 100:.0f}%",
                }
                self.risk_alerts.append(alert)
                logger.warning(f"⚠️ Risk Alert: {alert['message']}")

        except Exception as e:
            logger.error(f"Error checking position risk: {e}")

    async def _check_risk_alerts(self) -> None:
        """检查风险警报"""
        try:
            # 检查组合Delta
            total_delta = sum(
                pos.delta * pos.quantity for pos in self.positions.values()
            )
            if abs(total_delta) > self.max_portfolio_delta:
                alert = {
                    "type": "PORTFOLIO_DELTA",
                    "current_delta": total_delta,
                    "threshold": self.max_portfolio_delta,
                    "timestamp": datetime.now(),
                    "message": f"Portfolio delta {total_delta:.0f} exceeds limit {self.max_portfolio_delta}",
                }
                self.risk_alerts.append(alert)
                logger.warning(f"⚠️ Risk Alert: {alert['message']}")

            # 检查组合总亏损
            total_unrealized_pnl = sum(
                (pos.current_price - pos.entry_price) * pos.quantity * 100
                for pos in self.positions.values()
            )

            # 假设初始资金（需要从配置获取）
            initial_capital = 100000  # $100,000
            portfolio_loss_pct = total_unrealized_pnl / initial_capital

            if portfolio_loss_pct < -self.max_portfolio_loss:
                alert = {
                    "type": "PORTFOLIO_LOSS",
                    "current_loss_pct": portfolio_loss_pct,
                    "threshold": -self.max_portfolio_loss,
                    "timestamp": datetime.now(),
                    "message": f"Portfolio loss {portfolio_loss_pct * 100:.1f}% exceeds limit {self.max_portfolio_loss * 100:.0f}%",
                }
                self.risk_alerts.append(alert)
                logger.warning(f"⚠️ Risk Alert: {alert['message']}")

        except Exception as e:
            logger.error(f"Error checking risk alerts: {e}")

    def get_current_performance(self) -> Dict:
        """获取当前性能报告"""
        if not self.performance_history:
            return {"error": "No performance data available"}

        latest_metrics = self.performance_history[-1]["metrics"]

        # 添加仓位详情
        position_summary = []
        for pos_id, pos in self.positions.items():
            pnl = (pos.current_price - pos.entry_price) * pos.quantity * 100
            pnl_pct = (pos.current_price - pos.entry_price) / pos.entry_price

            position_summary.append(
                {
                    "position_id": pos_id,
                    "symbol": pos.symbol,
                    "type": pos.option_type,
                    "strike": pos.strike,
                    "expiry": pos.expiry,
                    "quantity": pos.quantity,
                    "entry_price": pos.entry_price,
                    "current_price": pos.current_price,
                    "pnl": pnl,
                    "pnl_pct": pnl_pct,
                    "delta": pos.delta,
                    "theta": pos.theta,
                }
            )

        return {
            "timestamp": datetime.now(),
            "performance_metrics": latest_metrics,
            "current_positions": position_summary,
            "recent_alerts": self.risk_alerts[-5:] if self.risk_alerts else [],
            "portfolio_greeks": {
                "total_delta": sum(
                    pos.delta * pos.quantity for pos in self.positions.values()
                ),
                "total_gamma": sum(
                    pos.gamma * pos.quantity for pos in self.positions.values()
                ),
                "total_theta": sum(
                    pos.theta * pos.quantity for pos in self.positions.values()
                ),
                "total_vega": sum(
                    pos.vega * pos.quantity for pos in self.positions.values()
                ),
            },
        }

    async def get_current_positions_from_ibkr(
        self, ibkr_config
    ) -> List[OptionsPosition]:
        """从IBKR获取当前期权仓位"""
        try:
            from core.broker import IBKRClient

            client = IBKRClient(ibkr_config)
            if not await client.connect():
                logger.error("Failed to connect to IBKR for position monitoring")
                return []

            try:
                # 获取所有仓位
                positions = await client.get_positions()
                options_positions = []

                for pos in positions:
                    # 检查是否为期权仓位
                    if (
                        hasattr(pos.contract, "secType")
                        and pos.contract.secType == "OPT"
                    ):
                        # 获取期权详情
                        option_details = await client.get_option_details(pos.contract)

                        if option_details:
                            # 创建OptionsPosition对象
                            options_position = OptionsPosition(
                                symbol=pos.contract.symbol,
                                option_type="CALL"
                                if pos.contract.right == "C"
                                else "PUT",
                                strike=float(pos.contract.strike),
                                expiry=pos.contract.lastTradeDateOrContractMonth,
                                quantity=int(pos.position),
                                entry_price=float(pos.avgCost) if pos.avgCost else 0.0,
                                current_price=float(option_details.get("price", 0)),
                                entry_time=datetime.now(),  # 需要从交易记录获取
                                underlying_price=float(
                                    option_details.get("underlying_price", 0)
                                ),
                                implied_volatility=float(
                                    option_details.get("iv", 0.25)
                                ),
                                delta=float(option_details.get("delta", 0)),
                                theta=float(option_details.get("theta", 0)),
                                gamma=float(option_details.get("gamma", 0)),
                                vega=float(option_details.get("vega", 0)),
                            )

                            options_positions.append(options_position)

                            # 添加到监控系统
                            position_id = f"{options_position.symbol}_{options_position.option_type}_{options_position.strike}_{options_position.expiry}"
                            self.positions[position_id] = options_position

                logger.info(f"📊 从IBKR获取到 {len(options_positions)} 个期权仓位")
                return options_positions

            finally:
                client.disconnect()

        except Exception as e:
            logger.error(f"Error getting positions from IBKR: {e}")
            return []

    async def update_all_positions_from_ibkr(self, ibkr_config) -> bool:
        """从IBKR更新所有仓位的实时价格"""
        try:
            from core.broker import IBKRClient

            if not self.positions:
                logger.info("No positions to update")
                return True

            client = IBKRClient(ibkr_config)
            if not await client.connect():
                logger.error("Failed to connect to IBKR for price updates")
                return False

            try:
                updated_count = 0

                for position_id, position in self.positions.items():
                    try:
                        # 获取期权实时价格
                        option_data = await client.get_option_price(
                            position.symbol,
                            position.option_type,
                            position.strike,
                            position.expiry,
                        )

                        if option_data:
                            # 更新价格和希腊字母
                            position.current_price = float(
                                option_data.get("price", position.current_price)
                            )
                            position.underlying_price = float(
                                option_data.get(
                                    "underlying_price", position.underlying_price
                                )
                            )
                            position.implied_volatility = float(
                                option_data.get("iv", position.implied_volatility)
                            )
                            position.delta = float(
                                option_data.get("delta", position.delta)
                            )
                            position.theta = float(
                                option_data.get("theta", position.theta)
                            )
                            position.gamma = float(
                                option_data.get("gamma", position.gamma)
                            )
                            position.vega = float(
                                option_data.get("vega", position.vega)
                            )

                            updated_count += 1

                    except Exception as e:
                        logger.warning(f"Failed to update position {position_id}: {e}")
                        continue

                logger.info(
                    f"✅ 更新了 {updated_count}/{len(self.positions)} 个仓位价格"
                )

                # 更新性能指标
                await self.update_performance_metrics()

                return True

            finally:
                client.disconnect()

        except Exception as e:
            logger.error(f"Error updating positions from IBKR: {e}")
            return False

    async def check_exit_conditions(self) -> List[Dict]:
        """检查所有仓位的出场条件 - 使用配置化的出场策略"""
        exit_signals = []

        try:
            from options.exit_config import DEFAULT_EXIT_CONDITIONS, ExitReason

            for position_id, position in self.positions.items():
                # 计算当前盈亏
                current_pnl_pct = (
                    position.current_price - position.entry_price
                ) / position.entry_price
                days_to_expiry = self._days_to_expiry(position.expiry)

                # 检查所有配置的出场条件
                triggered_conditions = []

                for condition in DEFAULT_EXIT_CONDITIONS:
                    should_exit = False

                    # 检查不同类型的出场条件
                    if (
                        condition.reason == ExitReason.TAKE_PROFIT_25PCT
                        and current_pnl_pct >= 0.25
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.TAKE_PROFIT_50PCT
                        and current_pnl_pct >= 0.50
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.TAKE_PROFIT_75PCT
                        and current_pnl_pct >= 0.75
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.STOP_LOSS_25PCT
                        and current_pnl_pct <= -0.25
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.STOP_LOSS_50PCT
                        and current_pnl_pct <= -0.50
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.STOP_LOSS_75PCT
                        and current_pnl_pct <= -0.75
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.TIME_DECAY_EXIT
                        and days_to_expiry <= condition.threshold
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.DELTA_RISK_EXIT
                        and abs(position.delta) > condition.threshold
                    ):
                        should_exit = True
                    elif (
                        condition.reason == ExitReason.IV_COLLAPSE_EXIT
                        and position.implied_volatility < condition.threshold
                    ):
                        should_exit = True

                    if should_exit:
                        triggered_conditions.append(condition)

                # 如果有触发的条件，选择最高优先级的
                if triggered_conditions:
                    # 按紧急程度排序，选择最紧急的
                    urgency_order = {"CRITICAL": 4, "HIGH": 3, "MEDIUM": 2, "LOW": 1}
                    primary_condition = max(
                        triggered_conditions,
                        key=lambda c: urgency_order.get(c.urgency.value, 0),
                    )

                    exit_signal = {
                        "position_id": position_id,
                        "position": position,
                        "exit_reason": primary_condition.reason.value,
                        "exit_description": primary_condition.description,
                        "current_pnl_pct": current_pnl_pct,
                        "recommended_exit_price": position.current_price,
                        "urgency": primary_condition.urgency.value,
                        "auto_execute": primary_condition.auto_execute,
                        "days_to_expiry": days_to_expiry,
                        "triggered_conditions": [
                            c.reason.value for c in triggered_conditions
                        ],
                    }
                    exit_signals.append(exit_signal)

                    logger.info(f"🚨 出场信号: {position_id}")
                    logger.info(f"   原因: {primary_condition.description}")
                    logger.info(f"   盈亏: {current_pnl_pct * 100:.1f}%")
                    logger.info(f"   紧急程度: {primary_condition.urgency.value}")
                    logger.info(
                        f"   自动执行: {'是' if primary_condition.auto_execute else '否'}"
                    )
                    if len(triggered_conditions) > 1:
                        logger.info(
                            f"   其他触发条件: {[c.description for c in triggered_conditions[1:]]}"
                        )

            return exit_signals

        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")
            return []

    def _days_to_expiry(self, expiry_str: str) -> int:
        """计算距离到期的天数"""
        try:
            # 解析到期日期 (格式: YYYYMMDD 或 YYYY-MM-DD)
            if "-" in expiry_str:
                expiry_date = datetime.strptime(expiry_str, "%Y-%m-%d").date()
            else:
                expiry_date = datetime.strptime(expiry_str, "%Y%m%d").date()

            today = datetime.now().date()
            return (expiry_date - today).days

        except Exception as e:
            logger.warning(f"Error parsing expiry date {expiry_str}: {e}")
            return 30  # 默认30天

    def _calculate_exit_urgency(self, exit_reason: str, pnl_pct: float) -> str:
        """计算出场紧急程度"""
        if exit_reason == "STOP_LOSS_50PCT":
            return "HIGH"
        elif exit_reason == "TIME_DECAY_EXIT":
            return "HIGH"
        elif exit_reason == "DELTA_RISK_EXIT":
            return "MEDIUM"
        elif exit_reason == "TAKE_PROFIT_50PCT":
            return "MEDIUM"
        elif exit_reason == "IV_COLLAPSE_EXIT":
            return "LOW"
        else:
            return "LOW"

    async def execute_exit_trade(self, exit_signal: Dict, ibkr_config) -> bool:
        """执行出场交易"""
        try:
            from core.broker import IBKRClient

            position = exit_signal["position"]
            position_id = exit_signal["position_id"]

            logger.info(f"🚀 执行出场交易: {position_id}")
            logger.info(f"   原因: {exit_signal['exit_reason']}")
            logger.info(f"   数量: {position.quantity}")
            logger.info(f"   价格: ${exit_signal['recommended_exit_price']:.2f}")

            client = IBKRClient(ibkr_config)
            if not await client.connect():
                logger.error("Failed to connect to IBKR for exit trade")
                return False

            try:
                # 创建期权合约
                contract = await client.create_option_contract(
                    symbol=position.symbol,
                    option_type=position.option_type,
                    strike=position.strike,
                    expiry=position.expiry,
                )

                if not contract:
                    logger.error(f"Failed to create contract for {position_id}")
                    return False

                # 创建卖出订单 (平仓)
                order = await client.create_market_order(
                    action="SELL",  # 卖出平仓
                    quantity=abs(position.quantity),
                    order_type="MKT",  # 市价单确保成交
                )

                # 提交订单
                trade = await client.place_order(contract, order)

                if trade:
                    logger.info(f"✅ 出场订单已提交: Order ID {trade.order.orderId}")

                    # 等待订单成交
                    await asyncio.sleep(2)  # 等待成交确认

                    # 记录出场交易
                    await self.close_position(
                        position_id=position_id,
                        exit_price=exit_signal["recommended_exit_price"],
                        exit_time=datetime.now(),
                    )

                    return True
                else:
                    logger.error(f"Failed to place exit order for {position_id}")
                    return False

            finally:
                client.disconnect()

        except Exception as e:
            logger.error(
                f"Error executing exit trade for {exit_signal['position_id']}: {e}"
            )
            return False

    def generate_performance_chart(self, save_path: str = None) -> str:
        """生成性能图表"""
        try:
            if not self.closed_trades:
                logger.warning("No closed trades to chart")
                return None

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

            # 1. 累积盈亏曲线
            pnl_series = [trade["pnl"] for trade in self.closed_trades]
            cumulative_pnl = np.cumsum(pnl_series)
            ax1.plot(cumulative_pnl, linewidth=2, color="blue")
            ax1.set_title("Cumulative P&L")
            ax1.set_xlabel("Trade Number")
            ax1.set_ylabel("Cumulative P&L ($)")
            ax1.grid(True, alpha=0.3)

            # 2. 盈亏分布
            ax2.hist(pnl_series, bins=20, alpha=0.7, color="green", edgecolor="black")
            ax2.set_title("P&L Distribution")
            ax2.set_xlabel("P&L ($)")
            ax2.set_ylabel("Frequency")
            ax2.axvline(x=0, color="red", linestyle="--", alpha=0.7)

            # 3. 胜率统计
            wins = len([p for p in pnl_series if p > 0])
            losses = len([p for p in pnl_series if p < 0])
            ax3.pie(
                [wins, losses],
                labels=["Wins", "Losses"],
                autopct="%1.1f%%",
                colors=["green", "red"],
                startangle=90,
            )
            ax3.set_title("Win/Loss Ratio")

            # 4. 持仓时间分析
            holding_days = [trade["holding_days"] for trade in self.closed_trades]
            ax4.hist(
                holding_days, bins=15, alpha=0.7, color="orange", edgecolor="black"
            )
            ax4.set_title("Holding Period Distribution")
            ax4.set_xlabel("Days")
            ax4.set_ylabel("Frequency")

            plt.tight_layout()

            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches="tight")
                logger.info(f"Performance chart saved to {save_path}")

            return save_path

        except Exception as e:
            logger.error(f"Error generating performance chart: {e}")
            return None


# 使用示例
async def main():
    """测试性能监控器"""
    monitor = OptionsPerformanceMonitor()

    # 模拟添加仓位
    position = OptionsPosition(
        symbol="AAPL",
        option_type="CALL",
        strike=150.0,
        expiry="2024-02-16",
        quantity=10,
        entry_price=5.50,
        current_price=6.20,
        entry_time=datetime.now() - timedelta(days=5),
        underlying_price=148.50,
        implied_volatility=0.25,
        delta=0.65,
        theta=-0.05,
    )

    await monitor.add_position(position)

    # 模拟价格更新
    price_updates = {
        "AAPL": {
            "underlying_price": 152.30,
            "CALL_150.0_2024-02-16": {
                "price": 7.80,
                "iv": 0.28,
                "delta": 0.75,
                "theta": -0.08,
            },
        }
    }

    await monitor.update_position_prices(price_updates)

    # 获取性能报告
    performance = monitor.get_current_performance()
    print(json.dumps(performance, indent=2, default=str))


if __name__ == "__main__":
    asyncio.run(main())
