#!/usr/bin/env python3
"""
期权交易参数配置

期权交易系统的高级优化参数和策略配置模块。
定义各种期权交易策略的参数、风险控制设置、
出场条件和性能优化配置。支持多策略参数管理。
"""

from dataclasses import dataclass
from typing import Dict


@dataclass
class OptimizedOptionsConfig:
    """优化的期权交易配置"""

    # === 核心策略参数优化 ===
    min_correlation: float = (
        0.01  # 进一步降低最小相关系数到0.01（适应ML距离转换的实际范围）
    )
    min_divergence: float = (
        0.2  # 极大降低最小背离标准差到0.2，适应159只股票的实际背离分布
    )
    max_option_days: int = 30  # 减少最大期权天数到30天
    max_risk_per_trade: float = 0.012  # 降低每笔交易最大风险到1.2%

    # === 动态风险管理 ===
    enable_dynamic_risk: bool = True  # 启用动态风险管理
    volatility_risk_adjustment: bool = True  # 基于波动率调整风险
    correlation_confidence_threshold: float = 0.8  # 相关性置信度阈值

    # === 增强流动性要求 ===
    min_daily_volume: int = 1500  # 提高最小日交易量到1500手
    min_open_interest: int = 150  # 提高最小未平仓到150手
    max_bid_ask_spread: float = 0.10  # 最大买卖价差10%
    min_option_price: float = 0.75  # 最小期权价格$0.75

    # === 期权选择优化 ===
    optimal_moneyness_call: float = 1.04  # CALL期权最佳价外程度4%
    optimal_moneyness_put: float = 1.04  # PUT期权最佳价外程度4%
    max_moneyness_deviation: float = 0.03  # 最大价外偏差3%

    # === 时间管理优化 ===
    min_time_to_expiry: int = 15  # 最小到期时间15天
    optimal_time_to_expiry: int = 22  # 最佳到期时间22天
    max_time_to_expiry: int = 30  # 最大到期时间30天

    # === 隐含波动率管理 ===
    min_implied_volatility: float = 0.15  # 最小隐含波动率15%
    max_implied_volatility: float = 0.50  # 最大隐含波动率50%
    optimal_implied_volatility: float = 0.28  # 最佳隐含波动率28%

    # === 策略数量控制 ===
    max_strategies: int = 3  # 减少最大策略数量到3个
    max_same_underlying: int = 1  # 同一标的最多1个策略

    # === 评分权重优化 ===
    scoring_weights: Dict[str, float] = None

    def __post_init__(self):
        if self.scoring_weights is None:
            self.scoring_weights = {
                "liquidity": 0.30,  # 流动性权重
                "divergence": 0.25,  # 背离度权重
                "confidence": 0.15,  # 置信度权重
                "spread": 0.12,  # 价差权重
                "moneyness": 0.10,  # 价位权重
                "volatility": 0.05,  # 波动率权重
                "time_decay": 0.03,  # 时间衰减权重
            }


@dataclass
class RiskManagementRules:
    """风险管理规则"""

    # === 仓位限制 ===
    max_options_allocation: float = 0.08  # 期权最大资金分配8%
    max_single_strategy_risk: float = 0.015  # 单策略最大风险1.5%
    max_sector_options_exposure: float = 0.04  # 单板块期权最大暴露4%

    # === 止损规则 ===
    options_stop_loss: float = 0.50  # 期权止损50%
    time_based_stop_loss: int = 7  # 时间止损7天
    correlation_breakdown_threshold: float = 0.3  # 相关性破裂阈值

    # === 市场条件限制 ===
    max_market_volatility: float = 0.35  # 最大市场波动率35%
    min_market_liquidity_score: float = 0.7  # 最小市场流动性评分

    # === 动态调整参数 ===
    volatility_adjustment_factors: Dict[str, float] = None

    def __post_init__(self):
        if self.volatility_adjustment_factors is None:
            self.volatility_adjustment_factors = {
                "low_vol": 1.2,  # 低波动率时风险倍数
                "normal_vol": 1.0,  # 正常波动率时风险倍数
                "high_vol": 0.7,  # 高波动率时风险倍数
                "extreme_vol": 0.4,  # 极端波动率时风险倍数
            }


@dataclass
class PerformanceOptimization:
    """性能优化配置"""

    # === 数据获取优化 ===
    enable_options_data_cache: bool = True  # 启用期权数据缓存
    cache_expiry_minutes: int = 5  # 缓存过期时间5分钟
    max_concurrent_option_requests: int = 10  # 最大并发期权请求数

    # === 计算优化 ===
    enable_parallel_scoring: bool = True  # 启用并行评分计算
    batch_size_option_analysis: int = 50  # 期权分析批次大小

    # === 监控优化 ===
    enable_real_time_monitoring: bool = True  # 启用实时监控
    monitoring_interval_seconds: int = 30  # 监控间隔30秒

    # === 报告优化 ===
    generate_detailed_reports: bool = True  # 生成详细报告
    save_strategy_charts: bool = True  # 保存策略图表
    chart_resolution_dpi: int = 300  # 图表分辨率


class OptionsOptimizationManager:
    """期权优化管理器"""

    def __init__(self):
        self.options_config = OptimizedOptionsConfig()
        self.risk_rules = RiskManagementRules()
        self.performance_config = PerformanceOptimization()

    def get_dynamic_risk_limit(
        self, market_volatility: float, strategy_confidence: float, days_to_expiry: int
    ) -> float:
        """计算动态风险限制"""
        base_risk = self.options_config.max_risk_per_trade

        # 波动率调整
        if market_volatility > 0.35:
            vol_factor = self.risk_rules.volatility_adjustment_factors["extreme_vol"]
        elif market_volatility > 0.25:
            vol_factor = self.risk_rules.volatility_adjustment_factors["high_vol"]
        elif market_volatility < 0.15:
            vol_factor = self.risk_rules.volatility_adjustment_factors["low_vol"]
        else:
            vol_factor = self.risk_rules.volatility_adjustment_factors["normal_vol"]

        # 置信度调整
        confidence_factor = min(1.5, max(0.5, strategy_confidence * 1.8))

        # 时间调整
        if days_to_expiry < 10:
            time_factor = 0.5
        elif days_to_expiry > 25:
            time_factor = 0.8
        else:
            time_factor = 1.0

        # 综合风险限制
        dynamic_risk = base_risk * vol_factor * confidence_factor * time_factor

        return max(0.005, min(0.025, dynamic_risk))

    def validate_strategy_quality(
        self,
        strategy_score: float,
        liquidity_score: float,
        correlation_confidence: float,
    ) -> bool:
        """验证策略质量"""
        min_score = 0.6  # 最低综合评分
        min_liquidity = 0.7  # 最低流动性评分
        min_confidence = self.options_config.correlation_confidence_threshold

        return (
            strategy_score >= min_score
            and liquidity_score >= min_liquidity
            and correlation_confidence >= min_confidence
        )

    def get_optimal_contract_selection_criteria(self) -> Dict:
        """获取最佳合约选择标准"""
        return {
            "moneyness_range": {
                "call_min": self.options_config.optimal_moneyness_call
                - self.options_config.max_moneyness_deviation,
                "call_max": self.options_config.optimal_moneyness_call
                + self.options_config.max_moneyness_deviation,
                "put_min": self.options_config.optimal_moneyness_put
                - self.options_config.max_moneyness_deviation,
                "put_max": self.options_config.optimal_moneyness_put
                + self.options_config.max_moneyness_deviation,
            },
            "time_to_expiry": {
                "min": self.options_config.min_time_to_expiry,
                "optimal": self.options_config.optimal_time_to_expiry,
                "max": self.options_config.max_time_to_expiry,
            },
            "implied_volatility": {
                "min": self.options_config.min_implied_volatility,
                "optimal": self.options_config.optimal_implied_volatility,
                "max": self.options_config.max_implied_volatility,
            },
            "liquidity": {
                "min_volume": self.options_config.min_daily_volume,
                "min_oi": self.options_config.min_open_interest,
                "max_spread": self.options_config.max_bid_ask_spread,
            },
        }


# 全局优化配置实例
OPTIMIZED_OPTIONS_CONFIG = OptionsOptimizationManager()


def get_optimization_summary() -> str:
    """获取优化配置摘要"""
    config = OPTIMIZED_OPTIONS_CONFIG

    summary = f"""
🎯 期权交易优化配置摘要
{"=" * 50}

📊 核心策略参数:
  • 最小相关系数: {config.options_config.min_correlation}
  • 最小背离标准差: {config.options_config.min_divergence}
  • 最大期权天数: {config.options_config.max_option_days}
  • 每笔交易最大风险: {config.options_config.max_risk_per_trade * 100:.1f}%

🛡️ 风险管理规则:
  • 期权资金分配: {config.risk_rules.max_options_allocation * 100:.1f}%
  • 期权止损: {config.risk_rules.options_stop_loss * 100:.0f}%
  • 最大市场波动率: {config.risk_rules.max_market_volatility * 100:.0f}%

⚡ 流动性要求:
  • 最小日交易量: {config.options_config.min_daily_volume}手
  • 最小未平仓: {config.options_config.min_open_interest}手
  • 最大买卖价差: {config.options_config.max_bid_ask_spread * 100:.0f}%

🎯 期权选择标准:
  • 最佳价外程度: {config.options_config.optimal_moneyness_call * 100 - 100:.0f}%
  • 最佳到期时间: {config.options_config.optimal_time_to_expiry}天
  • 最佳隐含波动率: {config.options_config.optimal_implied_volatility * 100:.0f}%
"""
    return summary


if __name__ == "__main__":
    print(get_optimization_summary())
