#!/usr/bin/env python
import sys
import numpy as np
from typing import Union, List


def convert_currency(
    logp: np.array, xrate: np.array, type: str = "forward"
) -> np.array:
    """
    转换对数形式价格的货币。如果`type=forward`，转换从原始对数价格货币
    到汇率确定的货币。如果`type=backward`则相反。

    参数
    ----------
    logp: np.array
        股票的对数价格。
    xrate: np.array
        从股票货币到另一种货币的汇率。
    type: str
        转换类型。可以是`forward`或`backward`。

    返回
    -------
    返回转换后的对数价格。
    """
    if type == "forward":
        return logp + np.log(xrate)
    if type == "backward":
        return logp - np.log(xrate)
    raise Exception("转换类型 {} 无法识别。".format(type))


class ProgressBar:
    """
    显示百分比进度的进度条。
    """

    def __init__(self, iterations, text="completed"):
        self.text = text
        self.iterations = iterations
        self.prog_bar = "[]"
        self.fill_char = "-"
        self.width = 50
        self.__update_amount(0)
        self.elapsed = 1

    def completed(self):
        if self.elapsed > self.iterations:
            self.elapsed = self.iterations
        self.update_iteration(1)
        print("\r" + str(self), end="")
        sys.stdout.flush()
        print()

    def animate(self, iteration=None):
        if iteration is None:
            self.elapsed += 1
        else:
            self.elapsed += iteration

        print("\r" + str(self), end="")
        sys.stdout.flush()
        self.update_iteration()

    def update_iteration(self, val=None):
        val = val if val is not None else self.elapsed / float(self.iterations)
        self.__update_amount(val * 100.0)
        self.prog_bar += "  %s of %s %s" % (self.elapsed, self.iterations, self.text)

    def __update_amount(self, new_amount):
        percent_done = int(round((new_amount / 100.0) * 100.0))
        all_full = self.width - 2
        num_hashes = int(round((percent_done / 100.0) * all_full))
        self.prog_bar = (
            "[" + self.fill_char * num_hashes + " " * (all_full - num_hashes) + "]"
        )
        pct_place = (len(self.prog_bar) // 2) - len(str(percent_done))
        pct_string = "%d%%" % percent_done
        self.prog_bar = self.prog_bar[0:pct_place] + (
            pct_string + self.prog_bar[pct_place + len(pct_string) :]
        )

    def __str__(self):
        return str(self.prog_bar)


def extract_hierarchical_info(sectors: dict, industries: dict) -> dict:
    """
    Extract information about sectors and industries useful to construct the probabilistic model.

    Parameters
    ----------
    sectors: dict
        Dict of sectors at stock-level.
    industries: dict
        Dict of industries at stock-level.

    Returns
    -------
    It returns a dictionary including the following keys:
    - num_sectors: number of unique sectors;
    - num_industries: number of unique industries;
    - sectors_id: a list of indices at stock-level, corresponding to the sector they belong to;
    - industries_id: a list of indices at stock-level, corresponding to the industries they belong to;
    - sector_industries_id; a list of indices at industry-level, corresponding to the sectors they belong to;
    - unique_sectors: array of unique sector names;
    - unique_industries: array of unique industry names.
    """
    # 处理空输入的情况
    if not sectors or not industries:
        logger.warning("Empty sectors or industries data, using default values")
        return dict(
            num_stocks=0,
            num_sectors=1,
            num_industries=1,
            sectors_id=[0],
            industries_id=[0],
            sector_industries_id=[0],
            unique_sectors=np.array(['Unknown']),
            unique_industries=np.array(['Unknown'])
        )

    # find unique names of sectors
    usectors = np.unique(list(sectors.values()))
    num_sectors = len(usectors)

    # 处理空的unique sectors
    if num_sectors == 0:
        logger.warning("No unique sectors found, using default")
        usectors = np.array(['Unknown'])
        num_sectors = 1

    # provide sector IDs at stock-level
    sectors_id = []
    for sector in sectors.values():
        matches = np.where(usectors == sector)[0]
        if len(matches) > 0:
            sectors_id.append(matches[0])
        else:
            sectors_id.append(0)  # 默认到第一个sector

    # find unique names of industries and store indices
    uindustries, industries_idx = np.unique(
        list(industries.values()), return_index=True
    )
    num_industries = len(uindustries)

    # 处理空的unique industries
    if num_industries == 0:
        logger.warning("No unique industries found, using default")
        uindustries = np.array(['Unknown'])
        industries_idx = np.array([0])
        num_industries = 1

    # provide industry IDs at stock-level
    industries_id = []
    for industry in industries.values():
        matches = np.where(uindustries == industry)[0]
        if len(matches) > 0:
            industries_id.append(matches[0])
        else:
            industries_id.append(0)  # 默认到第一个industry

    # provide sector IDs at industry-level
    if len(sectors_id) > 0 and len(industries_idx) > 0:
        sector_industries_id = np.array(sectors_id)[industries_idx].tolist()
    else:
        sector_industries_id = [0]

    # place relevant information in dictionary
    return dict(
        num_stocks=len(sectors),
        num_sectors=num_sectors,
        num_industries=num_industries,
        industries_id=industries_id,
        sectors_id=sectors_id,
        sector_industries_id=sector_industries_id,
        unique_sectors=usectors,
        unique_industries=uindustries,
    )


def compute_risk(portfolio: dict, variances: dict, sectors: dict, industries: dict):
    """
    It computes a portfolio risk measure.

    Parameters
    ----------
    portfolio: dict
        A dictionary with tickers as keys and another dictionary as values. The latter must include the following pairs:
        - "units": number of owned units of the corresponding stock.
    variances: dict
        A dictionary with tickers as keys and another dictionary as values. The latter must include the following pairs:
        - "stock": variance at stock level;
        - "industry": variance at industry level;
        - "sector": variance at sector level;
        - "market: variance at market level.
    sectors:
        A dictionary of tickers as keys and corresponding sectors as values.
    industries:
        A dictionary of tickers as keys and corresponding industries as values.

    Returns
    -------
    risk: float
        A positive number indicating the computed risk of the portfolio.
    """
    risk = 0
    for t1 in portfolio:
        for t2 in portfolio:
            tmp = variances[t1]["market"]
            if t1 == t2:
                tmp += variances[t1]["stock"]
            if industries[t1] == industries[t2]:
                tmp += variances[t1]["industry"]
                if sectors[t1] == sectors[t2]:
                    tmp += variances[t1]["sector"]
            risk += portfolio[t1]["units"] * portfolio[t2]["units"] * tmp
    return np.sqrt(risk) / max(len(portfolio), 1)


def calculate_correlation(data1: Union[List, np.ndarray], data2: Union[List, np.ndarray]) -> float:
    """
    计算两个数据序列的相关性

    参数:
        data1: 第一个数据序列
        data2: 第二个数据序列

    返回:
        相关系数 (-1到1之间)
    """
    try:
        # 转换为numpy数组
        arr1 = np.array(data1)
        arr2 = np.array(data2)

        # 检查数据有效性
        if len(arr1) != len(arr2) or len(arr1) == 0:
            return 0.0

        # 移除NaN值
        mask = ~(np.isnan(arr1) | np.isnan(arr2))
        if np.sum(mask) < 2:
            return 0.0

        arr1_clean = arr1[mask]
        arr2_clean = arr2[mask]

        # 计算相关系数
        correlation_matrix = np.corrcoef(arr1_clean, arr2_clean)
        correlation = correlation_matrix[0, 1]

        # 处理NaN结果
        if np.isnan(correlation):
            return 0.0

        return float(correlation)

    except Exception as e:
        print(f"Error calculating correlation: {e}")
        return 0.0
