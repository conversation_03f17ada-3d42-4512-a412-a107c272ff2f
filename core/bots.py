import numpy as np
import asyncio
import logging
from typing import Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

# 导入IBKR客户端用于真实交易
try:
    from core.broker import IBKRClient
    from core.config import IBKRConfig, DEFAULT_CONFIG
    IBKR_AVAILABLE = True
except ImportError:
    IBKR_AVAILABLE = False

class Bot:
    """
    这是一个具有可选真实交易功能的通用机器人结构。
    """
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config: IBKRConfig = None):
        self.capital = capital
        self.uninvested = capital
        self.invested = 0
        self.portfolio = {} if portfolio is None else portfolio

        # 真实交易设置
        self.real_trading = real_trading and IBKR_AVAILABLE
        self.ibkr_client = None
        self.ibkr_config = ibkr_config or DEFAULT_CONFIG
        self.trade_log = []

        if self.real_trading:
            self.ibkr_client = IBKRClient(self.ibkr_config)
            print(f"Bot initialized for {'PAPER' if self.ibkr_config.paper_trading else 'LIVE'} trading")

    async def connect_to_ibkr(self) -> bool:
        """连接到IBKR进行真实交易"""
        if self.real_trading and self.ibkr_client:
            return await self.ibkr_client.connect()
        return False

    def disconnect_from_ibkr(self):
        """断开与IBKR的连接"""
        if self.real_trading and self.ibkr_client:
            self.ibkr_client.disconnect()

    def transact_capital(self, ticker, units: int, price: float, type: str):
        """执行交易（模拟或真实）"""
        # Validate transaction type
        if type not in ["sell", "buy"]:
            raise Exception("Transaction type {} not recognised. Choose between `sell` and `buy`.".format(type))

        # Log the transaction
        trade_record = {
            'timestamp': datetime.now(),
            'ticker': ticker,
            'units': units,
            'price': price,
            'type': type,
            'real_trade': self.real_trading
        }

        # Execute real trade if enabled
        if self.real_trading and self.ibkr_client:
            try:
                # This will be called asynchronously in the real trading loop
                self._queue_real_trade(ticker, units, price, type)
            except Exception as e:
                print(f"Real trade failed for {ticker}: {e}")
                trade_record['error'] = str(e)

        # Always log the trade
        if not hasattr(self, 'trade_log'):
            self.trade_log = []

        # Update simulation portfolio regardless
        if type == "sell":
            transaction = units * price
            self.uninvested += transaction
            if ticker in self.portfolio:
                del self.portfolio[ticker]

        elif type == "buy":
            transaction = units * price
            self.uninvested -= transaction
            self.portfolio[ticker] = {"units": units, "purchase_price": price}

        # Log the trade
        self.trade_log.append(trade_record)

        # Determine mode based on IBKR port
        if self.ibkr_config and hasattr(self.ibkr_config, 'port'):
            mode = "LIVE" if self.ibkr_config.port == 7496 else "PAPER"
        else:
            mode = "REAL" if self.real_trading else "SIM"

        print(f"{mode} {type.upper()}: {units} shares of {ticker} at ${price:.2f}")

    def _queue_real_trade(self, ticker: str, units: int, price: float, trade_type: str):
        """将真实交易加入执行队列 (异步调用)"""
        if not hasattr(self, '_trade_queue'):
            self._trade_queue = []

        self._trade_queue.append({
            'ticker': ticker,
            'units': units,
            'price': price,
            'type': trade_type
        })

    async def execute_queued_trades(self):
        """执行所有队列中的真实交易"""
        if not self.real_trading or not hasattr(self, '_trade_queue'):
            return

        if not self._trade_queue:
            return

        # Check IBKR connection before executing trades
        if not self.ibkr_client or not self.ibkr_client.connected:
            print(f"❌ Cannot execute trades: IBKR client not connected")
            return

        executed_trades = 0
        failed_trades = 0

        while self._trade_queue:
            trade = self._trade_queue.pop(0)
            try:
                action = 'BUY' if trade['type'] == 'buy' else 'SELL'

                # Validate trade parameters
                if trade['units'] <= 0:
                    print(f"❌ Invalid trade units: {trade['units']} for {trade['ticker']}")
                    failed_trades += 1
                    continue

                if trade['price'] <= 0:
                    print(f"❌ Invalid trade price: {trade['price']} for {trade['ticker']}")
                    failed_trades += 1
                    continue

                # Use market order for now (could be enhanced with limit orders)
                result = await self.ibkr_client.place_market_order(
                    trade['ticker'],
                    trade['units'],
                    action
                )

                if result:
                    print(f"✅ Real trade executed: {action} {trade['units']} {trade['ticker']} @ ${trade['price']:.2f}")
                    executed_trades += 1

                    # Update trade log with execution status
                    trade['executed'] = True
                    trade['execution_time'] = datetime.now()
                    self.trade_log.append(trade)
                else:
                    print(f"❌ Real trade failed: {action} {trade['units']} {trade['ticker']}")
                    failed_trades += 1

                    # Log failed trade
                    trade['executed'] = False
                    trade['execution_time'] = datetime.now()
                    trade['error'] = 'Execution failed'
                    self.trade_log.append(trade)

            except Exception as e:
                print(f"❌ Error executing real trade {trade['ticker']}: {e}")
                failed_trades += 1

                # Log error
                trade['executed'] = False
                trade['execution_time'] = datetime.now()
                trade['error'] = str(e)
                self.trade_log.append(trade)

        print(f"📊 Trade execution summary: {executed_trades} executed, {failed_trades} failed")

    def compute_capital(self, price: dict):
        self.invested = 0.0
        for ticker in self.portfolio:
            self.invested += self.portfolio[ticker]['units'] * price[ticker]
        self.capital = self.uninvested + self.invested

class Adam(Bot):
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config = None):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.03
        self.max_rel_loss = 0.1

    def trade(self, info: dict):
        ## sell strategy
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # Check if ticker data is available
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]['price'] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]['units'], info[ticker]['price'], type="sell")
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## buy strategy
        for ticker in info:
            if ticker not in self.portfolio.keys() and info[ticker]['rate'] == "HIGHLY BELOW TREND":
                units = int(np.minimum(self.uninvested, self.capital / 30) // info[ticker]['price'])
                if units >= 1:
                    self.transact_capital(ticker, units, info[ticker]['price'], type="buy")

class Betty(Bot):
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config = None):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.1
        self.max_rel_loss = 0.03

    def trade(self, info: dict):
        ## sell strategy
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # Check if ticker data is available
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]['price'] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]['units'], info[ticker]['price'], type="sell")
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## buy strategy
        for ticker in info:
            if ticker not in self.portfolio.keys() and info[ticker]['rate'] == "HIGHLY ABOVE TREND":
                units = int(np.minimum(self.uninvested, self.capital / 30) // info[ticker]['price'])
                if units >= 1:
                    self.transact_capital(ticker, units, info[ticker]['price'], type="buy")

class Chris(Bot):
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config = None):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.buy_only = ["GOOGL", "AMZN", "AAPL", "MSFT", "META"]

    def trade(self, info: dict):
        ## buy strategy
        buy_only = [ticker for ticker in self.buy_only if ticker in info]
        for ticker in buy_only:
            if ticker in info:
                count = np.maximum(1, len(buy_only) - len(self.portfolio))
                units = int(self.uninvested / count // info[ticker]['price'])
                if units >= 1:
                    self.transact_capital(ticker, units, info[ticker]['price'], type="buy")

class Dany(Bot):
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config = None):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.1
        self.max_rel_loss = 0.2

    def trade(self, info: dict):
        ## sell strategy
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # Check if ticker data is available
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]['price'] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]['units'], info[ticker]['price'], type="sell")
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## buy strategy
        for ticker in info:
            if ticker not in self.portfolio.keys() and info[ticker]['rate'] in ["HIGHLY BELOW TREND", "BELOW TREND"]:
                units = int(np.minimum(self.uninvested, self.capital / 30) // info[ticker]['price'])
                if units >= 1:
                    self.transact_capital(ticker, units, info[ticker]['price'], type="buy")

class Eddy(Bot):
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config = None):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.2
        self.max_rel_loss = 0.1

    def trade(self, info: dict):
        ## sell strategy
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # Check if ticker data is available
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]['price'] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]['units'], info[ticker]['price'], type="sell")
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## buy strategy
        for ticker in info:
            if ticker not in self.portfolio.keys() and info[ticker]['rate'] in ["HIGHLY ABOVE TREND", "ABOVE TREND"]:
                units = int(np.minimum(self.uninvested, self.capital / 30) // info[ticker]['price'])
                if units >= 1:
                    self.transact_capital(ticker, units, info[ticker]['price'], type="buy")

class Flora(Bot):
    def __init__(self, capital: float, portfolio: dict = None,
                 real_trading: bool = False, ibkr_config = None):
        super().__init__(capital, portfolio, real_trading, ibkr_config)
        self.min_rel_profit = 0.1
        self.max_rel_loss = 0.2

    def trade(self, info: dict):
        ## sell strategy
        owned_stocks = list(self.portfolio.keys())
        for ticker in owned_stocks:
            if ticker in info:  # Check if ticker data is available
                purchase_price = self.portfolio[ticker]["purchase_price"]
                rel_margin = (info[ticker]['price'] - purchase_price) / purchase_price
                if rel_margin > self.min_rel_profit or rel_margin < -self.max_rel_loss:
                    self.transact_capital(ticker, self.portfolio[ticker]['units'], info[ticker]['price'], type="sell")
            else:
                logger.warning(f"No price data available for owned stock {ticker}")

        ## buy strategy
        growths = np.array([info[ticker]['growth'] for ticker in info])
        idx = np.argsort(growths)[::-1]
        sorted_tickers = np.array(list(info.keys()))[idx]
        for ticker in sorted_tickers:
            if ticker not in self.portfolio.keys() and info[ticker]['rate'] == "ALONG TREND" and info[ticker]['growth'] >= 1:
                units = int(np.minimum(self.uninvested, self.capital / 30) // info[ticker]['price'])
                if units >= 1:
                    self.transact_capital(ticker, units, info[ticker]['price'], type="buy")