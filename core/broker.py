#!/usr/bin/env python
"""
Interactive Brokers API 客户端封装器
"""
import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union, Tuple
import nest_asyncio

# 导入 ib_insync 并处理错误
try:
    from ib_insync import IB, Stock, Option, MarketOrder, LimitOrder, StopOrder, util
    from ib_insync.contract import Contract
    from ib_insync.order import Order
    IB_INSYNC_AVAILABLE = True
except ImportError as e:
    print(f"Warning: ib_insync not available: {e}")
    print("Please install with: pip install ib_insync")
    IB_INSYNC_AVAILABLE = False
    # 创建虚拟类以防止导入错误
    class IB: pass
    class Stock: pass
    class MarketOrder: pass
    class LimitOrder: pass
    class Contract: pass
    class Order: pass
    util = None

from core.config import IBKRConfig, DEFAULT_CONFIG

# 启用嵌套事件循环以兼容 Jupyter
nest_asyncio.apply()


class IBKRClient:
    """Interactive Brokers API 客户端封装器"""

    def __init__(self, config: IBKRConfig = None):
        self.config = config or DEFAULT_CONFIG

        # 如果未指定则自动分配客户端ID
        if self.config.client_id is None:
            import random
            import time

            # 使用时间戳 + 随机数确保唯一性
            self.config.client_id = int(str(int(time.time()))[-3:]) + random.randint(
                1, 99
            )

        self.ib = IB()
        self.connected = False
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """设置日志配置 - 使用现有的根日志器配置"""
        logger = logging.getLogger("IBKRClient")
        logger.setLevel(getattr(logging, self.config.log_level))

        # 如果根日志器已配置处理器则不添加处理器
        # 这可以防止重复的日志文件并尊重主系统的日志配置
        root_logger = logging.getLogger()
        if root_logger.handlers:
            # 使用现有的根日志器配置
            return logger

        # 后备方案：仅在没有根配置时添加处理器
        if not logger.handlers:
            # 在有组织的目录结构中创建日志文件
            from datetime import datetime
            import os

            date_str = datetime.now().strftime("%Y%m%d")
            log_dir = f"results/{date_str}/logs"

            # 确保日志目录存在
            os.makedirs(log_dir, exist_ok=True)

            log_file = f"{log_dir}/ibkr_trading.log"

            handler = logging.FileHandler(log_file)
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

            # 同时记录到控制台
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        return logger

    def _validate_config(self) -> bool:
        """验证 IBKR 配置参数"""
        try:
            # 检查主机
            if not self.config.host or not isinstance(self.config.host, str):
                self.logger.error("Invalid host configuration")
                return False

            # 检查端口
            if not isinstance(self.config.port, int) or self.config.port <= 0:
                self.logger.error(f"Invalid port configuration: {self.config.port}")
                return False

            # 验证常见的 IBKR 端口
            valid_ports = [7496, 7497, 4001, 4002]  # 实盘, 纸上交易, 网关实盘, 网关纸上交易
            if self.config.port not in valid_ports:
                self.logger.warning(f"Unusual port {self.config.port}, expected one of {valid_ports}")

            # 如果指定了超时时间则检查
            if hasattr(self.config, 'timeout'):
                if not isinstance(self.config.timeout, (int, float)) or self.config.timeout <= 0:
                    self.logger.error(f"Invalid timeout configuration: {self.config.timeout}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Configuration validation error: {e}")
            return False

    async def connect(self) -> bool:
        """连接到 TWS/IB Gateway"""
        if not IB_INSYNC_AVAILABLE:
            self.logger.error("ib_insync not available - cannot connect to IBKR")
            return False

        # 验证配置
        if not self._validate_config():
            return False

        try:
            self.ib = IB()

            # 生成动态客户端ID以避免冲突
            import random
            import time

            # 使用时间戳 + 随机数确保跨进程唯一性
            base_id = int(str(int(time.time()))[-4:])  # 时间戳的最后4位数字
            dynamic_client_id = self.config.client_id or (base_id + random.randint(1, 999))

            # 如果有冲突则尝试多个客户端ID
            max_attempts = 10
            for attempt in range(max_attempts):
                try:
                    self.logger.info(f"Connecting to {self.config.host}:{self.config.port} with clientId {dynamic_client_id}...")
                    await self.ib.connectAsync(
                        host=self.config.host,
                        port=self.config.port,
                        clientId=dynamic_client_id,
                    )
                    break  # Success, exit retry loop
                except Exception as e:
                    if "already in use" in str(e) or "326" in str(e):
                        # 生成更唯一的客户端ID
                        dynamic_client_id = int(str(int(time.time()))[-4:]) + random.randint(1000, 9999)
                        self.logger.warning(f"Client ID conflict, trying {dynamic_client_id} (attempt {attempt + 1})")
                        if attempt < max_attempts - 1:
                            await asyncio.sleep(2)  # Wait longer before retry
                            continue
                    raise e  # Re-raise if not a client ID conflict
            self.connected = True
            self.logger.info(
                f"Connected to IBKR at {self.config.host}:{self.config.port}"
            )

            # 设置事件处理器以过滤零仓位
            self.ib.positionEvent.clear()  # Clear default handlers
            self.ib.updatePortfolioEvent.clear()  # Clear default handlers
            self.ib.positionEvent += self._on_position_update
            self.ib.updatePortfolioEvent += self._on_portfolio_update

            # 检查是否为纸上交易
            account_summary = self.ib.accountSummary()
            if account_summary:
                account_type = next(
                    (
                        item.value
                        for item in account_summary
                        if item.tag == "AccountType"
                    ),
                    "Unknown",
                )
                self.logger.info(f"Account type: {account_type}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to IBKR: {e}")
            self.connected = False
            return False

    def _on_position_update(self, position):
        """Handle position updates - only log non-zero positions"""
        if position.position != 0:
            self.logger.info(
                f"Position update: {position.contract.symbol} = {position.position} shares"
            )

    def _on_portfolio_update(self, portfolio_item):
        """Handle portfolio updates - only log non-zero positions"""
        if portfolio_item.position != 0:
            self.logger.info(
                f"Portfolio update: {portfolio_item.contract.symbol} = {portfolio_item.position} shares, "
                f"value=${portfolio_item.marketValue:.2f}"
            )

    def disconnect(self):
        """Disconnect from TWS/IB Gateway"""
        if self.connected:
            self.ib.disconnect()
            self.connected = False
            self.logger.info("Disconnected from IBKR")

    def create_stock_contract(self, symbol: str, exchange: str = "SMART") -> Stock:
        """Create a stock contract"""
        return Stock(symbol, exchange, "USD")

    async def get_historical_data(
        self, symbol: str, duration: str = "1 Y", bar_size: str = "1 day", what_to_show: str = "ADJUSTED_LAST"
    ) -> Optional[pd.DataFrame]:
        """
        Get historical data for a symbol

        Parameters:
        -----------
        symbol: str
            Stock symbol
        duration: str
            Duration string (e.g., '1 Y', '6 M', '30 D')
        bar_size: str
            Bar size (e.g., '1 day', '1 hour', '5 mins')
        what_to_show: str
            What to show (e.g., 'ADJUSTED_LAST', 'TRADES', 'MIDPOINT')
        """
        try:
            contract = self.create_stock_contract(symbol)

            # 验证合约
            await self.ib.qualifyContractsAsync(contract)

            # 请求历史数据
            bars = await self.ib.reqHistoricalDataAsync(
                contract,
                endDateTime="",
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=True,
                formatDate=1,
            )

            if not bars:
                self.logger.warning(f"No historical data received for {symbol}")
                return None

            # 转换为DataFrame
            df = util.df(bars)
            df.set_index("date", inplace=True)
            df.index = pd.to_datetime(df.index)

            self.logger.info(f"Retrieved {len(df)} bars for {symbol}")
            return df

        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return None

    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for a symbol"""
        try:
            contract = self.create_stock_contract(symbol)
            await self.ib.qualifyContractsAsync(contract)

            # 请求市场数据
            ticker = self.ib.reqMktData(contract, "", False, False)
            await asyncio.sleep(2)  # Wait for data

            if ticker.marketPrice():
                price = ticker.marketPrice()
                self.ib.cancelMktData(contract)
                return price
            else:
                self.logger.warning(f"No market price available for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None

    async def place_market_order(
        self, symbol: str, quantity: int, action: str = "BUY"
    ) -> Optional[Order]:
        """
        Place a market order

        Parameters:
        -----------
        symbol: str
            Stock symbol
        quantity: int
            Number of shares
        action: str
            'BUY' or 'SELL'
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            await self.ib.qualifyContractsAsync(contract)

            order = MarketOrder(action, quantity)
            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} market order for {quantity} shares of {symbol}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing market order for {symbol}: {e}")
            return None

    async def place_limit_order(
        self, symbol: str, quantity: int, limit_price: float, action: str = "BUY"
    ) -> Optional[Order]:
        """
        Place a limit order

        Parameters:
        -----------
        symbol: str
            Stock symbol
        quantity: int
            Number of shares
        limit_price: float
            Limit price
        action: str
            'BUY' or 'SELL'
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            await self.ib.qualifyContractsAsync(contract)

            order = LimitOrder(action, quantity, limit_price)
            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} limit order for {quantity} shares of {symbol} at ${limit_price}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing limit order for {symbol}: {e}")
            return None



    def get_account_summary(self) -> Dict:
        """Get account summary information"""
        try:
            summary = self.ib.accountSummary()
            account_info = {}

            for item in summary:
                account_info[item.tag] = item.value

            return account_info

        except Exception as e:
            self.logger.error(f"Error getting account summary: {e}")
            return {}

    async def get_contract_details(self, symbol: str) -> Optional[List]:
        """Get detailed contract information for a symbol"""
        if not self.connected:
            return None

        try:
            # Create contract
            contract = Stock(symbol, "SMART", "USD")

            # Get contract details
            details = self.ib.reqContractDetails(contract)

            if details:
                return details
            else:
                self.logger.debug(f"No contract details found for {symbol}")
                return None

        except Exception as e:
            self.logger.debug(f"Failed to get contract details for {symbol}: {e}")
            return None

    def get_portfolio(self) -> List[Dict]:
        """Get current portfolio positions (only non-zero positions)"""
        if not self.connected:
            return []

        try:
            positions = self.ib.positions()
            portfolio = []

            for position in positions:
                if position.position != 0:  # Only include non-zero positions
                    portfolio.append(
                        {
                            "symbol": position.contract.symbol,
                            "position": position.position,
                            "avgCost": position.avgCost,
                            "currency": position.contract.currency,
                            "exchange": position.contract.exchange,
                        }
                    )

            return portfolio

        except Exception as e:
            self.logger.error(f"Error getting portfolio: {e}")
            return []

    async def get_positions(self) -> List:
        """Get all current positions from IBKR"""
        if not self.connected:
            return []

        try:
            # 获取所有仓位 - 使用正确的ib_insync API
            positions = self.ib.positions()
            return positions

        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    async def get_option_details(self, contract) -> Optional[Dict]:
        """Get detailed option information including Greeks"""
        if not self.connected:
            return None

        try:
            # 请求期权市场数据
            ticker = self.ib.reqMktData(contract, "", False, False)
            await asyncio.sleep(2)  # 等待数据

            if ticker:
                # 获取期权价格和希腊字母
                option_details = {
                    'price': ticker.marketPrice() or ticker.close or 0.0,
                    'bid': ticker.bid or 0.0,
                    'ask': ticker.ask or 0.0,
                    'volume': ticker.volume or 0,
                    'delta': getattr(ticker, 'delta', 0.0),
                    'gamma': getattr(ticker, 'gamma', 0.0),
                    'theta': getattr(ticker, 'theta', 0.0),
                    'vega': getattr(ticker, 'vega', 0.0),
                    'iv': getattr(ticker, 'impliedVolatility', 0.25)
                }

                # 获取标的股票价格
                if hasattr(contract, 'symbol'):
                    underlying_price = await self.get_current_price(contract.symbol)
                    option_details['underlying_price'] = underlying_price or 0.0

                # 取消市场数据订阅
                self.ib.cancelMktData(contract)

                return option_details

        except Exception as e:
            self.logger.error(f"Error getting option details: {e}")
            return None

    async def create_option_contract(self, symbol: str, option_type: str, strike: float, expiry: str):
        """Create option contract"""
        try:
            from ib_insync import Option

            # 转换期权类型
            right = "C" if option_type.upper() == "CALL" else "P"

            # 转换到期日格式
            if '-' in expiry:
                # 从YYYY-MM-DD转换为YYYYMMDD
                expiry = expiry.replace('-', '')

            # 创建期权合约
            contract = Option(symbol, expiry, strike, right, "SMART")

            # 验证合约
            qualified = await self.ib.qualifyContractsAsync(contract)
            if qualified:
                return qualified[0]
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error creating option contract: {e}")
            return None

    async def get_option_price(self, symbol: str, option_type: str, strike: float, expiry: str) -> Optional[Dict]:
        """Get option price and Greeks"""
        try:
            # 创建期权合约
            contract = await self.create_option_contract(symbol, option_type, strike, expiry)
            if not contract:
                return None

            # 获取期权详情
            return await self.get_option_details(contract)

        except Exception as e:
            self.logger.error(f"Error getting option price: {e}")
            return None

    async def place_stop_loss_order(
        self, symbol: str, quantity: int, stop_price: float, action: str = "SELL"
    ) -> Optional[Order]:
        """
        Place a stop loss order

        Parameters:
        -----------
        symbol: str
            Stock symbol
        quantity: int
            Number of shares
        stop_price: float
            Stop loss trigger price
        action: str
            Order action (usually "SELL" for stop loss)
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return None

        try:
            # Create contract
            contract = Stock(symbol, "SMART", "USD")
            await self.ib.qualifyContractsAsync(contract)

            # Create stop loss order
            order = StopOrder(action, quantity, stop_price)

            trade = self.ib.placeOrder(contract, order)

            self.logger.info(
                f"Placed {action} stop loss order for {quantity} shares of {symbol} at ${stop_price}"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing stop loss order for {symbol}: {e}")
            return None

    async def modify_stop_loss_order(self, symbol: str, new_stop_price: float) -> bool:
        """
        Modify existing stop loss order for a symbol

        Parameters:
        -----------
        symbol: str
            Stock symbol
        new_stop_price: float
            New stop loss trigger price
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return False

        try:
            # Get all open orders
            trades = self.ib.openTrades()

            # Find stop loss order for this symbol
            for trade in trades:
                if (trade.contract.symbol == symbol and
                    hasattr(trade.order, 'auxPrice') and
                    trade.order.orderType == 'STP'):

                    # Modify the order
                    trade.order.auxPrice = new_stop_price
                    self.ib.placeOrder(trade.contract, trade.order)

                    self.logger.info(f"Modified stop loss for {symbol} to ${new_stop_price}")
                    return True

            self.logger.warning(f"No stop loss order found for {symbol}")
            return False

        except Exception as e:
            self.logger.error(f"Error modifying stop loss for {symbol}: {e}")
            return False

    async def get_options_chain(self, symbol: str, expiry_date: str = None) -> Optional[list]:
        """
        Get options chain for a symbol - improved version

        Parameters:
        -----------
        symbol: str
            Stock symbol
        expiry_date: str
            Expiry date in YYYY-MM-DD format (optional)
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return None

        try:
            # Create stock contract first
            stock = Stock(symbol, "SMART", "USD")
            qualified_stocks = await self.ib.qualifyContractsAsync(stock)

            if not qualified_stocks:
                self.logger.error(f"Cannot qualify stock contract for {symbol}")
                return None

            stock = qualified_stocks[0]

            # Get option chain parameters
            chains = await self.ib.reqSecDefOptParamsAsync(
                stock.symbol, "", stock.secType, stock.conId
            )

            if not chains:
                self.logger.warning(f"No options chain found for {symbol}")
                return None

            # Get the first chain (usually the most liquid exchange)
            chain = chains[0]
            self.logger.info(f"Found options chain for {symbol}: {len(chain.expirations)} expiries, {len(chain.strikes)} strikes")

            # Filter expiries - get next 2-3 monthly expiries
            expiries = sorted(chain.expirations)[:3]
            if expiry_date:
                target_expiry = expiry_date.replace("-", "")
                expiries = [exp for exp in expiries if exp == target_expiry]

            # Filter strikes - get reasonable range around current price
            current_price = await self.get_current_price(symbol)
            if current_price:
                # Get strikes within ±20% of current price
                min_strike = current_price * 0.8
                max_strike = current_price * 1.2
                strikes = [s for s in chain.strikes if min_strike <= s <= max_strike]
            else:
                # If no current price, use middle range of strikes
                strikes = sorted(chain.strikes)[len(chain.strikes)//4:3*len(chain.strikes)//4]

            self.logger.info(f"Using {len(expiries)} expiries and {len(strikes)} strikes")

            # Create option contracts
            options = []
            for expiry in expiries:
                for strike in strikes[:20]:  # Limit strikes to avoid too many requests
                    # Create call and put contracts
                    call = Option(symbol, expiry, strike, "C", chain.exchange)
                    put = Option(symbol, expiry, strike, "P", chain.exchange)
                    options.extend([call, put])

            # Qualify contracts in batches to avoid rate limits
            qualified_options = []
            batch_size = 50

            for i in range(0, len(options), batch_size):
                batch = options[i:i+batch_size]
                try:
                    qualified_batch = await self.ib.qualifyContractsAsync(*batch)
                    qualified_options.extend(qualified_batch)
                    await asyncio.sleep(0.5)  # Rate limiting
                except Exception as e:
                    self.logger.warning(f"Failed to qualify batch {i//batch_size + 1}: {e}")

            self.logger.info(f"Successfully qualified {len(qualified_options)} options for {symbol}")
            return qualified_options

        except Exception as e:
            self.logger.error(f"Error getting options chain for {symbol}: {e}")
            return None

    async def place_options_order(
        self, symbol: str, expiry: str, strike: float, right: str,
        quantity: int, action: str = "BUY", order_type: str = "MKT", limit_price: float = None
    ) -> Optional[Order]:
        """
        Place an options order

        Parameters:
        -----------
        symbol: str
            Stock symbol
        expiry: str
            Expiry date in YYYYMMDD format
        strike: float
            Strike price
        right: str
            "C" for Call, "P" for Put
        quantity: int
            Number of contracts
        action: str
            "BUY" or "SELL"
        order_type: str
            "MKT" for market, "LMT" for limit
        limit_price: float
            Limit price (required for limit orders)
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return None

        try:
            # Create option contract
            contract = Option(symbol, expiry, strike, right, "SMART")
            await self.ib.qualifyContractsAsync(contract)

            # Create order
            if order_type == "MKT":
                order = MarketOrder(action, quantity)
            elif order_type == "LMT":
                if limit_price is None:
                    raise ValueError("Limit price required for limit orders")
                order = LimitOrder(action, quantity, limit_price)
            else:
                raise ValueError(f"Unsupported order type: {order_type}")

            trade = self.ib.placeOrder(contract, order)

            option_desc = f"{right} {symbol} {expiry} {strike}"
            self.logger.info(
                f"Placed {action} {order_type} order for {quantity} {option_desc} contracts"
            )
            return trade

        except Exception as e:
            self.logger.error(f"Error placing options order: {e}")
            return None

    async def get_options_market_data(self, contracts: list) -> Dict:
        """
        Get market data for options contracts

        Parameters:
        -----------
        contracts: list
            List of option contracts
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return {}

        try:
            # Request market data for all contracts
            tickers = []
            for contract in contracts[:20]:  # Limit to avoid rate limits
                ticker = self.ib.reqMktData(contract)
                tickers.append(ticker)

            # Wait for data
            await asyncio.sleep(2)

            market_data = {}
            for ticker in tickers:
                if ticker.contract:
                    # 尝试获取价格数据，优先级：last > close > mid > 模拟价格
                    price = None
                    if ticker.last and ticker.last > 0:
                        price = ticker.last
                    elif ticker.close and ticker.close > 0:
                        price = ticker.close
                    elif ticker.bid and ticker.ask and ticker.bid > 0 and ticker.ask > 0:
                        price = (ticker.bid + ticker.ask) / 2
                    else:
                        # 如果没有市场数据，使用基于内在价值的模拟价格
                        price = self._estimate_option_price(ticker.contract)

                    if price and price > 0:
                        key = f"{ticker.contract.symbol}_{ticker.contract.lastTradeDateOrContractMonth}_{ticker.contract.strike}_{ticker.contract.right}"
                        market_data[key] = {
                            "symbol": ticker.contract.symbol,
                            "expiry": ticker.contract.lastTradeDateOrContractMonth,
                            "strike": ticker.contract.strike,
                            "right": ticker.contract.right,
                            "last_price": price,
                            "bid": ticker.bid if ticker.bid and ticker.bid > 0 else price * 0.95,
                            "ask": ticker.ask if ticker.ask and ticker.ask > 0 else price * 1.05,
                            "volume": ticker.volume if ticker.volume else 100,  # 模拟交易量
                            "open_interest": getattr(ticker, 'openInterest', 500)  # 模拟持仓量
                        }

            self.logger.info(f"Retrieved market data for {len(market_data)} options")
            return market_data

        except Exception as e:
            self.logger.error(f"Error getting options market data: {e}")
            return {}

    def _estimate_option_price(self, contract) -> float:
        """估算期权价格（当没有市场数据时）"""
        try:
            # 基于内在价值和时间价值的简单估算
            strike = contract.strike

            # 假设标的股票价格（实际应该获取真实价格）
            underlying_price = 100.0  # 简化假设

            # 计算内在价值
            if contract.right == "C":  # Call
                intrinsic_value = max(0, underlying_price - strike)
            else:  # Put
                intrinsic_value = max(0, strike - underlying_price)

            # 添加时间价值（简化估算）
            time_value = 2.0  # 固定时间价值

            estimated_price = intrinsic_value + time_value

            # 确保最小价格
            return max(0.5, estimated_price)

        except Exception as e:
            self.logger.warning(f"Error estimating option price: {e}")
            return 2.0  # 默认价格

    async def get_current_price(self, symbol: str) -> Optional[float]:
        """
        Get current stock price - handles market closed scenarios

        Parameters:
        -----------
        symbol: str
            Stock symbol
        """
        if not self.connected:
            self.logger.error("Not connected to IBKR")
            return None

        try:
            contract = self.create_stock_contract(symbol)
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)

            if not qualified_contracts:
                self.logger.error(f"Cannot qualify contract for {symbol}")
                return None

            contract = qualified_contracts[0]

            # Try to get real-time data first
            ticker = self.ib.reqMktData(contract)
            await asyncio.sleep(3)  # Wait longer for data

            price = None

            # Priority order for price data
            if ticker.last and ticker.last > 0:
                price = ticker.last
                price_type = "last"
            elif ticker.close and ticker.close > 0:
                price = ticker.close
                price_type = "close"
            elif ticker.bid and ticker.ask and ticker.bid > 0 and ticker.ask > 0:
                price = (ticker.bid + ticker.ask) / 2
                price_type = "mid"
            elif ticker.marketPrice and ticker.marketPrice > 0:
                price = ticker.marketPrice
                price_type = "market"

            # If no real-time data, try historical data
            if not price:
                self.logger.info(f"No real-time data for {symbol}, trying historical data")
                try:
                    from datetime import datetime, timedelta
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=5)  # Last 5 days

                    bars = await self.ib.reqHistoricalDataAsync(
                        contract,
                        endDateTime=end_date,
                        durationStr='5 D',
                        barSizeSetting='1 day',
                        whatToShow='TRADES',
                        useRTH=True
                    )

                    if bars:
                        price = bars[-1].close  # Last close price
                        price_type = "historical_close"

                except Exception as hist_e:
                    self.logger.warning(f"Historical data request failed: {hist_e}")

            # Clean up
            try:
                self.ib.cancelMktData(contract)
            except:
                pass

            if price and price > 0:
                self.logger.info(f"Price for {symbol}: ${price:.2f} ({price_type})")
                return price
            else:
                self.logger.warning(f"No valid price data available for {symbol}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None
