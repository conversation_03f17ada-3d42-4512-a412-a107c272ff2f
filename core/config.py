#!/usr/bin/env python
"""
智能股票交易系统配置模块

包含IBKR连接、数据下载、模型训练、交易策略、风险管理等所有配置参数。
支持环境变量配置和多种预设模式。

Configuration module for Intelligent Stock Trading System
"""
import os
from dataclasses import dataclass
from typing import Optional, Dict, List, Union

@dataclass
class IBKRConfig:
    """Interactive Brokers 连接配置

    配置IBKR TWS/Gateway连接参数、交易设置和风险管理参数
    """

    # 连接设置 (Connection Settings)
    host: str = '127.0.0.1'  # TWS/IB Gateway 主机地址
    port: int = 7497  # 端口号：7497=纸上交易，7496=实盘交易
    client_id: Optional[int] = None  # 客户端ID，None时自动分配
    timeout: int = 30  # 连接超时时间（秒）

    # 交易设置 (Trading Settings)
    paper_trading: bool = True  # 是否纸上交易（安全起见默认开启）
    max_position_size: float = 10000.0  # 单个仓位最大金额（美元）
    max_daily_trades: int = 50  # 每日最大交易次数

    # 风险管理 (Risk Management)
    max_portfolio_risk: float = 0.02  # 单笔交易最大投资组合风险（2%）
    stop_loss_pct: float = 0.05  # 止损百分比（5%）
    take_profit_pct: float = 0.10  # 止盈百分比（10%）
    max_sector_exposure: float = 0.3  # 单板块最大暴露（30%）
    max_single_position: float = 0.05  # 单股最大仓位（5%）

    # 数据设置 (Data Settings)
    use_ibkr_data: bool = True  # 使用IBKR数据而非Yahoo Finance
    fallback_to_yahoo: bool = True  # IBKR数据失败时回退到Yahoo
    data_duration_years: int = 3  # 历史数据年数

    # 性能设置 (Performance Settings)
    max_concurrent_requests: int = 50  # IBKR并发请求限制
    max_requests_per_10min: int = 60   # IBKR 10分钟请求限制
    enable_data_cache: bool = True  # 启用数据缓存
    cache_ttl_seconds: int = 300       # 缓存生存时间（5分钟）
    enable_adaptive_concurrent: bool = True  # 启用自适应并发
    enable_prefetch: bool = True  # 启用数据预取

    # 日志设置 (Logging Settings)
    log_level: str = 'INFO'  # 日志级别
    log_file: str = 'ibkr_trading.log'  # 日志文件名
    
    @classmethod
    def from_env(cls) -> 'IBKRConfig':
        """从环境变量创建配置"""
        return cls(
            host=os.getenv('IBKR_HOST', '127.0.0.1'),
            port=int(os.getenv('IBKR_PORT', '7497')),
            client_id=int(os.getenv('IBKR_CLIENT_ID', '0')) if os.getenv('IBKR_CLIENT_ID') else None,
            paper_trading=os.getenv('IBKR_PAPER_TRADING', 'True').lower() == 'true',
            max_position_size=float(os.getenv('IBKR_MAX_POSITION_SIZE', '10000.0')),
            max_daily_trades=int(os.getenv('IBKR_MAX_DAILY_TRADES', '50')),
            max_portfolio_risk=float(os.getenv('IBKR_MAX_PORTFOLIO_RISK', '0.02')),
            stop_loss_pct=float(os.getenv('IBKR_STOP_LOSS_PCT', '0.05')),
            take_profit_pct=float(os.getenv('IBKR_TAKE_PROFIT_PCT', '0.10')),
            use_ibkr_data=os.getenv('IBKR_USE_DATA', 'True').lower() == 'true',
            fallback_to_yahoo=os.getenv('IBKR_FALLBACK_YAHOO', 'True').lower() == 'true',
            log_level=os.getenv('IBKR_LOG_LEVEL', 'INFO'),
            log_file=os.getenv('IBKR_LOG_FILE', 'ibkr_trading.log')
        )

@dataclass
class IBKRPerformanceConfig:
    """IBKR API性能优化的高级配置"""

    # Rate Limiting Configuration
    MAX_CONCURRENT_REQUESTS: int = 50  # IBKR hard limit
    MAX_REQUESTS_PER_10MIN: int = 60   # IBKR rate limit
    BURST_LIMIT_2SEC: int = 6          # Undocumented IBKR limit
    STABILITY_LIMIT_500MS: int = 2     # Connection stability

    # Adaptive Performance
    ENABLE_ADAPTIVE_CONCURRENT: bool = True
    MIN_CONCURRENT: int = 10
    MAX_CONCURRENT: int = 50
    LATENCY_THRESHOLD_FAST: float = 2.0  # Seconds
    LATENCY_THRESHOLD_SLOW: float = 5.0  # Seconds
    CONCURRENT_ADJUSTMENT_STEP: int = 2

    # Caching Configuration
    ENABLE_DATA_CACHE: bool = True
    CACHE_TTL_SECONDS: int = 300       # 5 minutes
    MAX_CACHE_ENTRIES: int = 1000

    # Performance Tracking
    TRACK_REQUEST_LATENCIES: bool = True
    MAX_LATENCY_SAMPLES: int = 50
    PROGRESS_REPORT_INTERVAL: int = 50  # Report every N stocks

    # Error Handling
    MAX_RETRY_ATTEMPTS: int = 3
    PACING_VIOLATION_BACKOFF: int = 5   # Extra seconds for pacing violations
    EXPONENTIAL_BACKOFF_BASE: int = 2
    JITTER_MAX: float = 1.0              # Random jitter for backoff

    # Prefetch and Incremental Updates
    ENABLE_PREFETCH: bool = True
    ENABLE_INCREMENTAL_UPDATES: bool = True
    INCREMENTAL_UPDATE_THRESHOLD_HOURS: int = 6
    PREFETCH_THRESHOLD_HOURS: int = 12

    @classmethod
    def get_conservative_config(cls):
        """获取稳定操作的保守设置"""
        config = cls()
        config.MAX_CONCURRENT_REQUESTS = 20
        config.ENABLE_ADAPTIVE_CONCURRENT = False
        config.CACHE_TTL_SECONDS = 600  # 10 minutes
        config.MAX_RETRY_ATTEMPTS = 5
        return config

    @classmethod
    def get_aggressive_config(cls):
        """获取最大性能的激进设置"""
        config = cls()
        config.MAX_CONCURRENT_REQUESTS = 50
        config.ENABLE_ADAPTIVE_CONCURRENT = True
        config.CACHE_TTL_SECONDS = 180  # 3 minutes
        config.CONCURRENT_ADJUSTMENT_STEP = 5
        return config

# Default configuration instance
DEFAULT_CONFIG = IBKRConfig()
DEFAULT_PERFORMANCE_CONFIG = IBKRPerformanceConfig()

# Trading hours (Eastern Time)
TRADING_HOURS = {
    'market_open': '09:30',
    'market_close': '16:00',
    'pre_market_start': '04:00',
    'after_market_end': '20:00'
}

# Supported exchanges
SUPPORTED_EXCHANGES = [
    'NASDAQ',
    'NYSE',
    'AMEX',
    'SMART'  # IBKR's smart routing
]

# Performance recommendations based on account type and usage
PERFORMANCE_RECOMMENDATIONS = {
    "paper_trading": {
        "max_concurrent": 20,
        "description": "Conservative settings for paper trading",
        "cache_ttl": 300,
        "enable_adaptive": True,
        "enable_prefetch": True
    },
    "live_trading_small": {
        "max_concurrent": 30,
        "description": "Balanced settings for small live accounts",
        "cache_ttl": 180,
        "enable_adaptive": True,
        "enable_prefetch": True
    },
    "live_trading_large": {
        "max_concurrent": 50,
        "description": "Aggressive settings for large accounts",
        "cache_ttl": 120,
        "enable_adaptive": True,
        "enable_prefetch": True
    },
    "development": {
        "max_concurrent": 10,
        "description": "Safe settings for development",
        "cache_ttl": 60,
        "enable_adaptive": False,
        "enable_prefetch": False
    }
}

def get_recommended_config(account_type: str = "paper_trading") -> IBKRPerformanceConfig:
    """根据账户类型获取推荐配置"""

    if account_type not in PERFORMANCE_RECOMMENDATIONS:
        account_type = "paper_trading"

    rec = PERFORMANCE_RECOMMENDATIONS[account_type]

    config = IBKRPerformanceConfig()
    config.MAX_CONCURRENT_REQUESTS = rec["max_concurrent"]
    config.CACHE_TTL_SECONDS = rec["cache_ttl"]
    config.ENABLE_ADAPTIVE_CONCURRENT = rec["enable_adaptive"]
    config.ENABLE_PREFETCH = rec["enable_prefetch"]

    return config

# Common stock symbols for testing
TEST_SYMBOLS = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA',
    'META', 'NVDA', 'NFLX', 'ADBE', 'CRM'
]

# ============================================================================
# 机器学习模型配置 (Machine Learning Model Configuration)
# ============================================================================

@dataclass
class ModelConfig:
    """机器学习模型配置

    包含MSIS-MCS模型、回归预测模型、CNN-GRU等所有模型的配置参数
    """

    # MSIS-MCS 模型配置 (MSIS-MCS Model Configuration)
    order_scale: List[float] = None  # 多尺度参数 [1, 0.1, 0.01]
    num_levels: int = 4  # 层次数量（市场/板块/行业/个股）
    learning_rate: float = 0.01  # 学习率
    num_steps: int = 10000  # 训练步数
    batch_size: int = 32  # 批次大小

    # 正则化参数 (Regularization)
    l1_regularization: float = 0.001  # L1正则化
    l2_regularization: float = 0.001  # L2正则化
    dropout_rate: float = 0.1  # Dropout比率

    # 收敛控制 (Convergence Control)
    tolerance: float = 1e-6  # 收敛容忍度
    early_stopping: bool = True  # 早停
    patience: int = 100  # 早停耐心值

    # 回归预测模型配置 (Reversion Prediction Model)
    model_type: str = 'cnn_gru'  # 模型类型：'lstm', 'cnn_gru', 'ensemble'
    enable_ensemble: bool = False  # 启用集成预测（LSTM + CNN-GRU平均）
    ensemble_weights: List[float] = None  # 集成权重 [lstm_weight, cnn_gru_weight]

    # CNN-GRU 模型参数
    cnn_filters: List[int] = None  # CNN滤波器数量 [64, 32]
    cnn_kernel_size: int = 3  # CNN卷积核大小
    gru_units: List[int] = None  # GRU单元数 [128, 64]

    # LSTM 模型参数（备选）
    lstm_units: List[int] = None  # LSTM单元数 [128, 64, 32]

    # 训练参数 (Training Parameters)
    epochs: int = 200  # 训练轮数
    validation_split: float = 0.2  # 验证集比例
    early_stopping_patience: int = 30  # 早停耐心值
    use_time_series_split: bool = True  # 使用时间序列分割
    n_splits: int = 5  # 交叉验证分割数

    # 相关性分析配置 (Correlation Analysis)
    correlation_threshold: float = 0.6  # 相关性阈值
    divergence_threshold: float = 1.5   # 背离阈值
    reversion_threshold: float = 0.5    # 回归阈值
    min_divergence_days: int = 3        # 最小背离持续天数
    max_divergence_days: int = 120      # 最大背离持续天数（4个月）

    def __post_init__(self):
        """初始化默认值"""
        if self.order_scale is None:
            self.order_scale = [1.0, 0.1, 0.01]
        if self.cnn_filters is None:
            self.cnn_filters = [64, 32]
        if self.gru_units is None:
            self.gru_units = [128, 64]
        if self.lstm_units is None:
            self.lstm_units = [128, 64, 32]
        if self.ensemble_weights is None:
            self.ensemble_weights = [0.5, 0.5]  # 等权重

@dataclass
class TechnicalIndicatorConfig:
    """技术指标配置

    配置所有技术指标的计算参数
    """

    # RSI 配置
    rsi_period: int = 14  # RSI周期

    # MACD 配置
    macd_fast: int = 12  # MACD快线周期
    macd_slow: int = 26  # MACD慢线周期
    macd_signal: int = 9  # MACD信号线周期

    # 布林带配置
    bb_period: int = 20  # 布林带周期
    bb_std: float = 2.0  # 布林带标准差倍数

    # 移动平均配置
    sma_periods: List[int] = None  # SMA周期列表 [20, 50, 200]
    ema_periods: List[int] = None  # EMA周期列表 [12, 26]

    # 波动率配置
    atr_period: int = 14  # ATR周期
    volatility_window: int = 20  # 波动率计算窗口

    # 成交量配置
    volume_ma_period: int = 20  # 成交量移动平均周期

    # 动量指标配置
    momentum_period: int = 10  # 动量指标周期
    roc_period: int = 10  # 变化率周期
    cci_period: int = 14  # CCI周期
    williams_r_period: int = 14  # Williams %R周期

    def __post_init__(self):
        """初始化默认值"""
        if self.sma_periods is None:
            self.sma_periods = [20, 50, 200]
        if self.ema_periods is None:
            self.ema_periods = [12, 26]

# ============================================================================
# 趋势分类配置 (Trend Classification Configuration)
# ============================================================================

@dataclass
class TrendClassificationConfig:
    """趋势分类配置

    配置7级趋势分类系统的阈值和参数
    """

    # 极端级别设置 (Extreme Level Settings)
    extreme_level: str = "high"  # 极端标准：'high', 'medium', 'low'

    # 7级趋势分类阈值 (7-Level Trend Classification Thresholds)
    # 注意：这些是分数阈值，不是百分比分布
    trend_bounds_high: Dict[str, float] = None  # 高标准阈值
    trend_bounds_medium: Dict[str, float] = None  # 中等标准阈值
    trend_bounds_low: Dict[str, float] = None  # 低标准阈值

    # 分布目标百分比 (Target Distribution Percentages)
    target_distribution: Dict[str, tuple] = None  # 目标分布比例

    def __post_init__(self):
        """初始化默认阈值"""
        if self.trend_bounds_high is None:
            self.trend_bounds_high = {
                "EXTREME BELOW TREND": -40.0,   # ~0.5th percentile (ultra rare)
                "HIGHLY BELOW TREND": -23.0,    # ~1st percentile (very rare)
                "BELOW TREND": -10.0,           # ~5th percentile (rare)
                "ALONG TREND": 29.0,            # Normal range (-10 to 29)
                "ABOVE TREND": 37.0,            # ~99th percentile (rare)
                "HIGHLY ABOVE TREND": 42.0,     # ~99.5th percentile (very rare)
                "EXTREME ABOVE TREND": float("inf"),  # >99.5th percentile (ultra rare)
            }

        if self.trend_bounds_medium is None:
            self.trend_bounds_medium = {
                "EXTREME BELOW TREND": -30.0,   # ~1st percentile
                "HIGHLY BELOW TREND": -15.0,    # ~3rd percentile
                "BELOW TREND": -5.0,            # ~10th percentile
                "ALONG TREND": 25.0,            # Normal range (-5 to 25)
                "ABOVE TREND": 32.0,            # ~97th percentile
                "HIGHLY ABOVE TREND": 40.0,     # ~99th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >99th percentile
            }

        if self.trend_bounds_low is None:
            self.trend_bounds_low = {
                "EXTREME BELOW TREND": -23.0,   # ~1st percentile
                "HIGHLY BELOW TREND": -10.0,    # ~5th percentile
                "BELOW TREND": -3.0,            # ~12th percentile
                "ALONG TREND": 25.0,            # Normal range (-3 to 25)
                "ABOVE TREND": 32.0,            # ~95th percentile
                "HIGHLY ABOVE TREND": 37.0,     # ~99th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >99th percentile
            }

        if self.target_distribution is None:
            self.target_distribution = {
                'EXTREME_BELOW': (0.1, 0.6),    # 极度低于趋势：0.1-0.6%
                'HIGHLY_BELOW': (1, 2),         # 高度低于趋势：1-2%
                'BELOW_TREND': (5, 8),          # 低于趋势：5-8%
                'ALONG_TREND': (38, 79),        # 沿趋势：38-79%
                'ABOVE_TREND': (5, 8),          # 高于趋势：5-8%
                'HIGHLY_ABOVE': (1, 2),         # 高度高于趋势：1-2%
                'EXTREME_ABOVE': (0.1, 0.6),    # 极度高于趋势：0.1-0.6%
            }

    def get_trend_bounds(self) -> Dict[str, float]:
        """根据极端级别获取趋势阈值"""
        if self.extreme_level == "high":
            return self.trend_bounds_high
        elif self.extreme_level == "medium":
            return self.trend_bounds_medium
        else:
            return self.trend_bounds_low

# ============================================================================
# 交易机器人配置 (Trading Bot Configuration)
# ============================================================================

@dataclass
class BotConfig:
    """交易机器人配置基类"""

    # 基础配置
    name: str = "Bot"  # 机器人名称
    capital: float = 100000.0  # 初始资金
    max_positions: int = 30  # 最大持仓数量
    position_size_ratio: float = 1/30  # 每个仓位占总资金的比例

    # 交易信号配置
    buy_signals: List[str] = None  # 买入信号列表
    sell_signals: List[str] = None  # 卖出信号列表

    # 风险管理
    max_rel_loss: float = 0.05  # 最大相对亏损（5%）
    min_rel_profit: float = 0.1  # 最小相对盈利（10%）

    # 持仓管理
    hold_period_min: int = 1  # 最少持有天数
    hold_period_max: int = 30  # 最多持有天数

    def __post_init__(self):
        """初始化默认值"""
        if self.buy_signals is None:
            self.buy_signals = ["HIGHLY BELOW TREND"]
        if self.sell_signals is None:
            self.sell_signals = ["HIGHLY ABOVE TREND"]

@dataclass
class AdamBotConfig(BotConfig):
    """Adam机器人配置 - 保守型趋势跟踪"""

    name: str = "Adam"
    buy_signals: List[str] = None
    sell_signals: List[str] = None
    max_positions: int = 30
    position_size_ratio: float = 1/30
    hold_period_min: int = 1
    hold_period_max: int = 30

    def __post_init__(self):
        if self.buy_signals is None:
            self.buy_signals = ['HIGHLY BELOW TREND']
        if self.sell_signals is None:
            self.sell_signals = ['HIGHLY ABOVE TREND']

@dataclass
class BettyBotConfig(BotConfig):
    """Betty机器人配置 - 动态止盈止损"""

    name: str = "Betty"
    buy_signals: List[str] = None
    sell_signals: List[str] = None
    min_rel_profit: float = 0.1  # 最小止盈比例（10%）
    max_rel_loss: float = 0.03   # 最大止损比例（3%）
    trailing_stop: bool = True   # 启用跟踪止损
    trailing_distance: float = 0.02  # 跟踪止损距离（2%）

    def __post_init__(self):
        if self.buy_signals is None:
            self.buy_signals = ['HIGHLY BELOW TREND', 'BELOW TREND']
        if self.sell_signals is None:
            self.sell_signals = ['HIGHLY ABOVE TREND', 'ABOVE TREND']

@dataclass
class ChrisBotConfig(BotConfig):
    """Chris机器人配置 - 大盘股专注"""

    name: str = "Chris"
    buy_only: List[str] = None  # 只买入指定股票
    equal_weight: bool = True   # 等权重分配
    rebalance_frequency: str = 'weekly'  # 重新平衡频率
    min_cash_ratio: float = 0.1  # 最小现金比例

    def __post_init__(self):
        super().__post_init__()
        if self.buy_only is None:
            self.buy_only = ['GOOGL', 'AMZN', 'AAPL', 'MSFT', 'META']

@dataclass
class DanyBotConfig(BotConfig):
    """Dany机器人配置 - 快速交易"""

    name: str = "Dany"
    buy_signals: List[str] = None
    sell_signals: List[str] = None
    max_hold_days: int = 5  # 最大持有天数
    quick_profit_target: float = 0.05  # 快速止盈目标（5%）
    position_size_aggressive: bool = True  # 激进仓位管理
    min_rel_profit: float = 0.1
    max_rel_loss: float = 0.2

    def __post_init__(self):
        if self.buy_signals is None:
            self.buy_signals = ['EXTREME BELOW TREND', 'HIGHLY BELOW TREND']
        if self.sell_signals is None:
            self.sell_signals = ['EXTREME ABOVE TREND', 'HIGHLY ABOVE TREND']

@dataclass
class EddyBotConfig(BotConfig):
    """Eddy机器人配置 - 反向投资"""

    name: str = "Eddy"
    contrarian_signals: bool = True  # 反向信号
    buy_signals: List[str] = None
    sell_signals: List[str] = None
    patience_factor: float = 2.0  # 耐心因子
    risk_tolerance: str = 'high'  # 高风险容忍度

    def __post_init__(self):
        if self.buy_signals is None:
            self.buy_signals = ['EXTREME ABOVE TREND']
        if self.sell_signals is None:
            self.sell_signals = ['EXTREME BELOW TREND']

@dataclass
class FloraBotConfig(BotConfig):
    """Flora机器人配置 - 长期价值投资"""

    name: str = "Flora"
    value_metrics: List[str] = None  # 价值指标
    buy_signals: List[str] = None
    sell_signals: List[str] = None
    min_hold_period: int = 30  # 最少持有30天
    dividend_preference: bool = True  # 偏好分红股
    sector_diversification: bool = True  # 板块分散

    def __post_init__(self):
        if self.value_metrics is None:
            self.value_metrics = ['P/E', 'P/B', 'ROE']
        if self.buy_signals is None:
            self.buy_signals = ['BELOW TREND', 'ALONG TREND']
        if self.sell_signals is None:
            self.sell_signals = ['HIGHLY ABOVE TREND']

# ============================================================================
# 风险管理配置 (Risk Management Configuration)
# ============================================================================

@dataclass
class RiskConfig:
    """风险管理配置

    配置投资组合风险控制参数
    """

    # 仓位限制 (Position Limits)
    max_single_position: float = 0.05  # 单股最大仓位（5%）
    max_sector_exposure: float = 0.3   # 单板块最大暴露（30%）
    max_total_exposure: float = 0.95   # 最大总仓位（95%）
    min_cash_reserve: float = 0.05     # 最小现金储备（5%）

    # 止损止盈 (Stop Loss & Take Profit)
    global_stop_loss: float = 0.02     # 全局止损（2%）
    global_take_profit: float = 0.15   # 全局止盈（15%）
    trailing_stop_distance: float = 0.02  # 跟踪止损距离（2%）
    stop_loss_cooldown: int = 24       # 止损冷却期（小时）

    # 流动性控制 (Liquidity Control)
    min_daily_volume: int = 1000000    # 最小日成交量
    max_market_impact: float = 0.01    # 最大市场冲击（1%）
    order_split_threshold: int = 10000 # 大单拆分阈值

    # 风险指标 (Risk Metrics)
    max_portfolio_beta: float = 1.5    # 最大投资组合贝塔
    target_sharpe_ratio: float = 1.0   # 目标夏普比率
    max_drawdown_limit: float = 0.1    # 最大回撤限制（10%）
    var_confidence: float = 0.95       # VaR置信度

    # 相关性控制 (Correlation Control)
    max_correlation: float = 0.8       # 最大持仓相关性
    correlation_window: int = 60       # 相关性计算窗口
    rebalance_threshold: float = 0.05  # 重新平衡阈值

# ============================================================================
# 期权交易配置 (Options Trading Configuration)
# ============================================================================

@dataclass
class OptionsConfig:
    """期权交易配置

    配置期权套利和相关性交易参数
    """

    # 基础配置
    enable_options_trading: bool = False  # 启用期权交易
    min_correlation: float = 0.5         # 最小相关系数
    min_divergence: float = 1.0          # 最小背离标准差
    max_option_days: int = 45            # 最大期权天数
    max_risk_per_trade: float = 0.02     # 每笔交易最大风险（2%）

    # 流动性要求 (Liquidity Requirements)
    min_daily_volume: int = 1000         # 最小日交易量（手）
    min_open_interest: int = 100         # 最小未平仓合约（手）
    max_strategies: int = 5              # 最大策略数量

    # 期权类型偏好 (Option Type Preferences)
    prefer_monthly_expiry: bool = True   # 偏好月度到期
    max_moneyness: float = 0.1          # 最大价内/价外程度（10%）
    min_time_to_expiry: int = 7         # 最小到期时间（天）
    max_time_to_expiry: int = 45        # 最大到期时间（天）

# ============================================================================
# 系统监控配置 (System Monitoring Configuration)
# ============================================================================

@dataclass
class MonitoringConfig:
    """系统监控配置

    配置系统性能监控和报警参数
    """

    # 性能监控 (Performance Monitoring)
    cpu_threshold: int = 80             # CPU使用率阈值（%）
    memory_threshold: int = 80          # 内存使用率阈值（%）
    disk_threshold: int = 90            # 磁盘使用率阈值（%）

    # 连接监控 (Connection Monitoring)
    connection_timeout: int = 30        # 连接超时（秒）
    heartbeat_interval: int = 60        # 心跳间隔（秒）
    reconnect_attempts: int = 5         # 重连尝试次数
    reconnect_delay: int = 10           # 重连延迟（秒）

    # 交易监控 (Trading Monitoring)
    max_daily_loss: float = 0.05        # 每日最大亏损（5%）
    max_daily_trades: int = 100         # 每日最大交易数
    unusual_volume_threshold: float = 3.0  # 异常成交量阈值（倍数）
    price_gap_threshold: float = 0.1    # 价格跳空阈值（10%）

    # 报警设置 (Alert Settings)
    email_alerts: bool = True           # 启用邮件报警
    sms_alerts: bool = False            # 启用短信报警
    alert_cooldown: int = 300           # 报警冷却期（秒）
    critical_alerts: List[str] = None   # 关键报警类型

    def __post_init__(self):
        """初始化默认值"""
        if self.critical_alerts is None:
            self.critical_alerts = [
                'connection_lost',
                'large_loss',
                'system_error',
                'unusual_activity'
            ]

# ============================================================================
# 默认配置实例 (Default Configuration Instances)
# ============================================================================

# 基础配置实例
DEFAULT_MODEL_CONFIG = ModelConfig()
DEFAULT_TECHNICAL_CONFIG = TechnicalIndicatorConfig()
DEFAULT_TREND_CONFIG = TrendClassificationConfig()
DEFAULT_RISK_CONFIG = RiskConfig()
DEFAULT_OPTIONS_CONFIG = OptionsConfig()
DEFAULT_MONITORING_CONFIG = MonitoringConfig()

# 机器人配置实例
DEFAULT_ADAM_CONFIG = AdamBotConfig()
DEFAULT_BETTY_CONFIG = BettyBotConfig()
DEFAULT_CHRIS_CONFIG = ChrisBotConfig()
DEFAULT_DANY_CONFIG = DanyBotConfig()
DEFAULT_EDDY_CONFIG = EddyBotConfig()
DEFAULT_FLORA_CONFIG = FloraBotConfig()

# 机器人配置映射
BOT_CONFIGS = {
    'Adam': DEFAULT_ADAM_CONFIG,
    'Betty': DEFAULT_BETTY_CONFIG,
    'Chris': DEFAULT_CHRIS_CONFIG,
    'Dany': DEFAULT_DANY_CONFIG,
    'Eddy': DEFAULT_EDDY_CONFIG,
    'Flora': DEFAULT_FLORA_CONFIG,
}

# ============================================================================
# 配置工厂函数 (Configuration Factory Functions)
# ============================================================================

def get_model_config(model_type: str = 'cnn_gru', enable_ensemble: bool = False) -> ModelConfig:
    """获取模型配置

    Parameters:
    -----------
    model_type: str
        模型类型：'lstm', 'cnn_gru', 'ensemble'
    enable_ensemble: bool
        是否启用集成预测

    Returns:
    --------
    ModelConfig: 模型配置实例
    """
    config = ModelConfig()
    config.model_type = model_type
    config.enable_ensemble = enable_ensemble

    if enable_ensemble:
        config.model_type = 'ensemble'
        config.ensemble_weights = [0.4, 0.6]  # LSTM权重0.4, CNN-GRU权重0.6

    return config

def get_trend_config(extreme_level: str = 'high') -> TrendClassificationConfig:
    """获取趋势分类配置

    Parameters:
    -----------
    extreme_level: str
        极端级别：'high', 'medium', 'low'

    Returns:
    --------
    TrendClassificationConfig: 趋势分类配置实例
    """
    config = TrendClassificationConfig()
    config.extreme_level = extreme_level
    return config

def get_bot_config(bot_name: str) -> BotConfig:
    """获取机器人配置

    Parameters:
    -----------
    bot_name: str
        机器人名称：'Adam', 'Betty', 'Chris', 'Dany', 'Eddy', 'Flora'

    Returns:
    --------
    BotConfig: 机器人配置实例
    """
    if bot_name in BOT_CONFIGS:
        return BOT_CONFIGS[bot_name]
    else:
        # 默认返回Betty配置
        return DEFAULT_BETTY_CONFIG

def get_conservative_config() -> Dict:
    """获取保守配置组合

    适合新手和纸上交易
    """
    return {
        'ibkr': IBKRConfig(
            paper_trading=True,
            max_position_size=5000.0,
            max_daily_trades=20,
            stop_loss_pct=0.03,
            take_profit_pct=0.08
        ),
        'model': ModelConfig(
            model_type='lstm',  # 使用更稳定的LSTM
            enable_ensemble=False,
            epochs=100,
            early_stopping_patience=20
        ),
        'trend': TrendClassificationConfig(extreme_level='high'),
        'risk': RiskConfig(
            max_single_position=0.03,  # 更保守的3%
            max_sector_exposure=0.2,   # 更保守的20%
            global_stop_loss=0.015     # 更严格的1.5%止损
        ),
        'bot': 'Adam'  # 保守的Adam机器人
    }

def get_aggressive_config() -> Dict:
    """获取激进配置组合

    适合经验丰富的交易者
    """
    return {
        'ibkr': IBKRConfig(
            max_position_size=20000.0,
            max_daily_trades=100,
            stop_loss_pct=0.08,
            take_profit_pct=0.15
        ),
        'model': ModelConfig(
            model_type='ensemble',  # 使用集成模型
            enable_ensemble=True,
            epochs=300,
            early_stopping_patience=50
        ),
        'trend': TrendClassificationConfig(extreme_level='low'),  # 更多机会
        'risk': RiskConfig(
            max_single_position=0.08,  # 更激进的8%
            max_sector_exposure=0.4,   # 更激进的40%
            global_stop_loss=0.03      # 更宽松的3%止损
        ),
        'bot': 'Dany'  # 激进的Dany机器人
    }

def get_balanced_config() -> Dict:
    """获取平衡配置组合

    适合大多数用户的默认配置
    """
    return {
        'ibkr': DEFAULT_CONFIG,
        'model': ModelConfig(
            model_type='cnn_gru',  # 推荐的CNN-GRU模型
            enable_ensemble=False,
            epochs=200,
            early_stopping_patience=30
        ),
        'trend': DEFAULT_TREND_CONFIG,
        'risk': DEFAULT_RISK_CONFIG,
        'bot': 'Betty'  # 平衡的Betty机器人
    }

# ============================================================================
# 配置验证函数 (Configuration Validation Functions)
# ============================================================================

def validate_config(config_dict: Dict) -> bool:
    """验证配置的有效性

    Parameters:
    -----------
    config_dict: Dict
        配置字典

    Returns:
    --------
    bool: 配置是否有效
    """
    try:
        # 验证必要的配置项
        required_keys = ['ibkr', 'model', 'trend', 'risk', 'bot']
        for key in required_keys:
            if key not in config_dict:
                print(f"Missing required config: {key}")
                return False

        # 验证IBKR配置
        ibkr_config = config_dict['ibkr']
        if not isinstance(ibkr_config, IBKRConfig):
            print("Invalid IBKR config type")
            return False

        # 验证端口设置
        if ibkr_config.port not in [7496, 7497]:
            print(f"Invalid IBKR port: {ibkr_config.port}")
            return False

        # 验证模型配置
        model_config = config_dict['model']
        if not isinstance(model_config, ModelConfig):
            print("Invalid model config type")
            return False

        # 验证模型类型
        valid_model_types = ['lstm', 'cnn_gru', 'ensemble']
        if model_config.model_type not in valid_model_types:
            print(f"Invalid model type: {model_config.model_type}")
            return False

        # 验证机器人名称
        bot_name = config_dict['bot']
        if bot_name not in BOT_CONFIGS:
            print(f"Invalid bot name: {bot_name}")
            return False

        print("✅ Configuration validation passed")
        return True

    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

def print_config_summary(config_dict: Dict):
    """打印配置摘要

    Parameters:
    -----------
    config_dict: Dict
        配置字典
    """
    print("📋 Configuration Summary")
    print("=" * 50)

    # IBKR配置
    ibkr = config_dict.get('ibkr', DEFAULT_CONFIG)
    print(f"🔌 IBKR Connection:")
    print(f"   Host: {ibkr.host}:{ibkr.port}")
    print(f"   Mode: {'Paper Trading' if ibkr.paper_trading else 'Live Trading'}")
    print(f"   Max Position: ${ibkr.max_position_size:,.0f}")

    # 模型配置
    model = config_dict.get('model', DEFAULT_MODEL_CONFIG)
    print(f"🤖 Model Configuration:")
    print(f"   Type: {model.model_type}")
    print(f"   Ensemble: {'Yes' if model.enable_ensemble else 'No'}")
    print(f"   Epochs: {model.epochs}")

    # 趋势配置
    trend = config_dict.get('trend', DEFAULT_TREND_CONFIG)
    print(f"📈 Trend Classification:")
    print(f"   Extreme Level: {trend.extreme_level}")

    # 风险配置
    risk = config_dict.get('risk', DEFAULT_RISK_CONFIG)
    print(f"🛡️ Risk Management:")
    print(f"   Max Single Position: {risk.max_single_position*100:.1f}%")
    print(f"   Max Sector Exposure: {risk.max_sector_exposure*100:.1f}%")
    print(f"   Global Stop Loss: {risk.global_stop_loss*100:.1f}%")

    # 机器人配置
    bot_name = config_dict.get('bot', 'Betty')
    print(f"🤖 Trading Bot: {bot_name}")

    print("=" * 50)
