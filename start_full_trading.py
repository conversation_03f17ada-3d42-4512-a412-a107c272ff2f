#!/usr/bin/env python3
"""完整交易系统启动脚本

智能股票交易系统的主启动脚本，提供完整的自动化交易流程。
包含股票池更新、历史数据下载、机器学习模型分析、相关性计算
和期权交易的一站式执行。支持纸上交易和实盘交易模式。
"""

import argparse
import asyncio
import os
import sys
import time
from datetime import datetime


def setup_logging():
    """设置日志"""
    import logging

    # 创建日志目录
    log_dir = f"results/{datetime.now().strftime('%Y%m%d')}/logs"
    os.makedirs(log_dir, exist_ok=True)

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(f"{log_dir}/full_trading.log"),
            logging.StreamHandler(sys.stdout),
        ],
    )

    return logging.getLogger(__name__)


async def update_stock_universe(logger, max_symbols=None):
    """更新股票池"""
    logger.info("🔄 步骤1: 更新股票池...")

    try:
        from services.updater import StockUniverseUpdater

        updater = StockUniverseUpdater()
        await updater.run_full_update(max_symbols)

        logger.info("✅ 股票池更新完成")
        return True

    except Exception as e:
        logger.error(f"❌ 股票池更新失败: {e}")
        return False


async def run_full_trading_system(args, logger):
    """运行完整交易系统"""
    logger.info("🚀 步骤2: 启动完整交易系统...")

    try:
        # 导入必要模块
        from core.bots import Adam, Betty, Chris, Dany, Eddy, Flora
        from core.config import DEFAULT_CONFIG
        from stocks.trend import DailyTradingSystem

        # 选择交易机器人
        bot_classes = {
            "adam": Adam,
            "betty": Betty,
            "chris": Chris,
            "dany": Dany,
            "eddy": Eddy,
            "flora": Flora,
        }

        bot_class = bot_classes.get(args.bot.lower(), Betty)

        logger.info(f"🤖 Using bot: {bot_class.__name__}")
        logger.info(f"💰 Initial capital: ${args.capital:,.2f}")
        logger.info(f"🎯 期权交易: {'启用' if args.enable_options else '禁用'}")
        logger.info(f"📊 交易模式: {'纸上交易' if args.paper else '实盘交易'}")

        # 显示优化配置
        if args.enable_options:
            from options.params import get_optimization_summary

            print("\n" + "=" * 60)
            print("🎯 优化期权配置")
            print("=" * 60)
            print(get_optimization_summary())

        # 创建交易系统
        system = DailyTradingSystem(
            config=DEFAULT_CONFIG,
            bot_class=bot_class,
            initial_capital=args.capital,
            update_universe=False,  # 已经在Step 1中更新了
            extreme_level=args.extreme_level,
            skip_download=args.skip_download,  # 根据参数决定是否跳过下载
            enable_options=args.enable_options,
        )

        # 运行交易系统
        logger.info("🚀 开始完整交易分析...")
        success = await system.run_daily_analysis()

        if success:
            logger.info("✅ 交易分析成功完成")
            return True
        else:
            logger.error("❌ 交易分析失败")
            return False

    except Exception as e:
        logger.error(f"💥 运行交易系统错误: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main_workflow(args):
    """主工作流程"""
    logger = setup_logging()

    start_time = time.time()

    logger.info("🎯 启动完整交易系统工作流")
    logger.info("=" * 60)
    logger.info(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🤖 机器人: {args.bot}")
    logger.info(f"💰 资金: ${args.capital:,.2f}")
    logger.info(f"📊 模式: {'纸上交易' if args.paper else '实盘交易'}")
    logger.info(f"🎯 期权: {'启用' if args.enable_options else '禁用'}")
    logger.info("=" * 60)

    try:
        # Step 1: 更新股票池（如果需要）
        if args.update_universe:
            if not await update_stock_universe(logger, args.max_symbols):
                logger.error("❌ 工作流在步骤1失败")
                return False
        else:
            logger.info("⏭️ 跳过股票池更新 (使用 --update-universe 启用)")

        # Step 2: 运行完整交易系统
        if not await run_full_trading_system(args, logger):
            logger.error("❌ 工作流在步骤2失败")
            return False

        # 计算总时间
        elapsed_time = time.time() - start_time
        hours = int(elapsed_time // 3600)
        minutes = int((elapsed_time % 3600) // 60)
        seconds = int(elapsed_time % 60)

        logger.info("=" * 60)
        logger.info("🎉 完整交易工作流成功完成！")
        logger.info(f"⏱️ 总时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        logger.info(f"📊 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        return True

    except KeyboardInterrupt:
        logger.warning("⏹️ 工作流被用户中断")
        return False
    except Exception as e:
        logger.error(f"💥 工作流中出现意外错误: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="完整交易系统启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 完整流程：更新股票池 + 纸上交易 + 期权交易
  python start_full_trading.py --paper --enable-options --update-universe --bot betty --capital 100000
  
  # 仅运行交易（不更新股票池）
  python start_full_trading.py --paper --enable-options --bot betty --capital 100000
  
  # 限制股票数量（测试用）
  python start_full_trading.py --paper --enable-options --update-universe --max-symbols 1000
        """,
    )

    # 交易模式
    trading_group = parser.add_mutually_exclusive_group(required=True)
    trading_group.add_argument(
        "--paper", action="store_true", help="纸上交易模式 (IBKR端口7497)"
    )
    trading_group.add_argument(
        "--live", action="store_true", help="实盘交易模式 (IBKR端口7496)"
    )

    # 期权交易
    parser.add_argument(
        "--enable-options", action="store_true", help="启用期权交易 (使用优化配置)"
    )

    # 机器人选择
    parser.add_argument(
        "--bot",
        choices=["adam", "betty", "chris", "dany", "eddy", "flora"],
        default="betty",
        help="选择交易机器人 (默认: betty)",
    )

    # 资金设置
    parser.add_argument(
        "--capital", type=float, default=100000.0, help="初始资金 (默认: 100000)"
    )

    # 股票池更新
    parser.add_argument(
        "--update-universe", action="store_true", help="更新股票池（扫描全部IBKR股票）"
    )
    parser.add_argument("--max-symbols", type=int, help="最大股票数量（测试用）")

    # 其他选项
    parser.add_argument(
        "--extreme-level",
        choices=["high", "medium", "low"],
        default="high",
        help="极值检测级别 (默认: high)",
    )
    parser.add_argument(
        "--skip-download", action="store_true", help="跳过数据下载，使用缓存数据"
    )

    args = parser.parse_args()

    # 运行完整工作流程
    try:
        success = asyncio.run(main_workflow(args))
        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n⏹️ 交易工作流被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
