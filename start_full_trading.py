#!/usr/bin/env python3
"""
完整交易系统启动脚本

包含股票池更新、数据下载、分析和期权交易的完整流程
"""

import asyncio
import argparse
import sys
import os
import time
from datetime import datetime

def setup_logging():
    """设置日志"""
    import logging
    
    # 创建日志目录
    log_dir = f"results/{datetime.now().strftime('%Y%m%d')}/logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f"{log_dir}/full_trading.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

async def update_stock_universe(logger, max_symbols=None):
    """更新股票池"""
    logger.info("🔄 Step 1: Updating stock universe...")
    
    try:
        from services.updater import StockUniverseUpdater
        
        updater = StockUniverseUpdater()
        await updater.run_full_update(max_symbols)
        
        logger.info("✅ Stock universe update completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Stock universe update failed: {e}")
        return False

async def run_full_trading_system(args, logger):
    """运行完整交易系统"""
    logger.info("🚀 Step 2: Starting full trading system...")
    
    try:
        # 导入必要模块
        from stocks.trend import DailyTradingSystem
        from core.bots import Adam, Betty, Chris, Dany, Eddy, Flora
        from core.config import DEFAULT_CONFIG
        
        # 选择交易机器人
        bot_classes = {
            'adam': Adam,
            'betty': Betty,
            'chris': Chris,
            'dany': Dany,
            'eddy': Eddy,
            'flora': Flora
        }
        
        bot_class = bot_classes.get(args.bot.lower(), Betty)
        
        logger.info(f"🤖 Using bot: {bot_class.__name__}")
        logger.info(f"💰 Initial capital: ${args.capital:,.2f}")
        logger.info(f"🎯 Options trading: {'ENABLED' if args.enable_options else 'DISABLED'}")
        logger.info(f"📊 Trading mode: {'PAPER' if args.paper else 'LIVE'}")
        
        # 显示优化配置
        if args.enable_options:
            from options.params import get_optimization_summary
            print("\n" + "="*60)
            print("🎯 OPTIMIZED OPTIONS CONFIGURATION")
            print("="*60)
            print(get_optimization_summary())
        
        # 创建交易系统
        system = DailyTradingSystem(
            config=DEFAULT_CONFIG,
            bot_class=bot_class,
            initial_capital=args.capital,
            update_universe=False,  # 已经在Step 1中更新了
            extreme_level=args.extreme_level,
            skip_download=args.skip_download,    # 根据参数决定是否跳过下载
            enable_options=args.enable_options
        )
        
        # 运行交易系统
        logger.info("🚀 Starting complete trading analysis...")
        success = await system.run_daily_analysis()
        
        if success:
            logger.info("✅ Trading analysis completed successfully")
            return True
        else:
            logger.error("❌ Trading analysis failed")
            return False
            
    except Exception as e:
        logger.error(f"💥 Error running trading system: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main_workflow(args):
    """主工作流程"""
    logger = setup_logging()
    
    start_time = time.time()
    
    logger.info("🎯 Starting Full Trading System Workflow")
    logger.info("="*60)
    logger.info(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"🤖 Bot: {args.bot}")
    logger.info(f"💰 Capital: ${args.capital:,.2f}")
    logger.info(f"📊 Mode: {'PAPER' if args.paper else 'LIVE'}")
    logger.info(f"🎯 Options: {'ENABLED' if args.enable_options else 'DISABLED'}")
    logger.info("="*60)
    
    try:
        # Step 1: 更新股票池（如果需要）
        if args.update_universe:
            if not await update_stock_universe(logger, args.max_symbols):
                logger.error("❌ Workflow failed at Step 1")
                return False
        else:
            logger.info("⏭️ Skipping stock universe update (use --update-universe to enable)")
        
        # Step 2: 运行完整交易系统
        if not await run_full_trading_system(args, logger):
            logger.error("❌ Workflow failed at Step 2")
            return False
        
        # 计算总时间
        elapsed_time = time.time() - start_time
        hours = int(elapsed_time // 3600)
        minutes = int((elapsed_time % 3600) // 60)
        seconds = int(elapsed_time % 60)
        
        logger.info("="*60)
        logger.info("🎉 FULL TRADING WORKFLOW COMPLETED SUCCESSFULLY!")
        logger.info(f"⏱️ Total time: {hours:02d}:{minutes:02d}:{seconds:02d}")
        logger.info(f"📊 End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("="*60)
        
        return True
        
    except KeyboardInterrupt:
        logger.warning("⏹️ Workflow interrupted by user")
        return False
    except Exception as e:
        logger.error(f"💥 Unexpected error in workflow: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="完整交易系统启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 完整流程：更新股票池 + 纸上交易 + 期权交易
  python start_full_trading.py --paper --enable-options --update-universe --bot betty --capital 100000
  
  # 仅运行交易（不更新股票池）
  python start_full_trading.py --paper --enable-options --bot betty --capital 100000
  
  # 限制股票数量（测试用）
  python start_full_trading.py --paper --enable-options --update-universe --max-symbols 1000
        """
    )
    
    # 交易模式
    trading_group = parser.add_mutually_exclusive_group(required=True)
    trading_group.add_argument('--paper', action='store_true', 
                              help='纸上交易模式 (IBKR端口7497)')
    trading_group.add_argument('--live', action='store_true', 
                              help='实盘交易模式 (IBKR端口7496)')
    
    # 期权交易
    parser.add_argument('--enable-options', action='store_true',
                       help='启用期权交易 (使用优化配置)')
    
    # 机器人选择
    parser.add_argument('--bot', choices=['adam', 'betty', 'chris', 'dany', 'eddy', 'flora'],
                       default='betty', help='选择交易机器人 (默认: betty)')
    
    # 资金设置
    parser.add_argument('--capital', type=float, default=100000.0,
                       help='初始资金 (默认: 100000)')
    
    # 股票池更新
    parser.add_argument('--update-universe', action='store_true',
                       help='更新股票池（扫描全部IBKR股票）')
    parser.add_argument('--max-symbols', type=int,
                       help='最大股票数量（测试用）')
    
    # 其他选项
    parser.add_argument('--extreme-level', choices=['high', 'medium', 'low'],
                       default='high', help='极值检测级别 (默认: high)')
    parser.add_argument('--skip-download', action='store_true',
                       help='跳过数据下载，使用缓存数据')
    
    args = parser.parse_args()
    
    # 运行完整工作流程
    try:
        success = asyncio.run(main_workflow(args))
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Trading workflow interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
