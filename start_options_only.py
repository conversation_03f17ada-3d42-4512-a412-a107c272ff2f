#!/usr/bin/env python3
"""
独立期权交易启动脚本

只执行期权套利分析和交易，不执行股票交易。
基于ML验证的相关性背离进行期权套利。
"""

import argparse
import asyncio
import logging
import os
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def run_options_only_workflow(args):
    """独立期权交易工作流程"""
    logger.info("🎯 启动独立期权交易系统")
    logger.info("=" * 60)
    logger.info(f"💰 资金: ${args.capital:,.2f}")
    logger.info(f"📊 交易模式: {'纸上交易' if args.paper else '实盘交易'}")
    logger.info("=" * 60)

    try:
        # 导入必要模块
        from core.config import DEFAULT_CONFIG
        from options.arbitrage import OptionsArbitrageSystem
        from stocks.trend import DailyTradingSystem

        # 设置IBKR配置
        ibkr_config = DEFAULT_CONFIG
        ibkr_config.paper_trading = args.paper
        ibkr_config.port = 7497 if args.paper else 7496

        # 显示优化配置
        from options.params import get_optimization_summary

        print("\n" + "=" * 60)
        print("🎯 优化期权配置")
        print("=" * 60)
        print(get_optimization_summary())

        # 第一步：获取股票数据和ML分析结果
        logger.info("📊 第1步：获取股票数据和ML分析...")

        # 创建交易系统实例（仅用于数据获取）
        trading_system = DailyTradingSystem(
            config=ibkr_config,
            charts_dir=f"results/{datetime.now().strftime('%Y%m%d')}/charts",
        )

        # 获取股票数据
        if not args.skip_download:
            logger.info("📥 下载股票数据...")
            download_success = await trading_system.download_data()
            if not download_success:
                logger.error("❌ 数据下载失败")
                return False
        else:
            logger.info("⏭️ 跳过数据下载，使用缓存数据")

        # 运行ML分析
        logger.info("🧠 运行ML模型分析...")
        model_results = await trading_system.run_ml_analysis()
        if not model_results:
            logger.error("❌ ML分析失败")
            return False

        # 运行相关性分析
        logger.info("🔗 运行相关性分析...")
        correlation_analysis = await trading_system.run_correlation_analysis(
            model_results
        )
        if not correlation_analysis:
            logger.error("❌ 相关性分析失败")
            return False

        # 第二步：期权套利分析
        logger.info("🎯 第2步：期权套利分析...")

        # 创建期权套利系统
        options_system = OptionsArbitrageSystem(
            config=ibkr_config,
            charts_dir=f"results/{datetime.now().strftime('%Y%m%d')}/charts",
        )

        # 运行期权套利
        options_success = await options_system.run_options_arbitrage(
            correlation_analysis, model_results, args.capital
        )

        if options_success:
            logger.info("✅ 期权交易完成")
        else:
            logger.warning("⚠️ 期权交易未执行或失败")

        logger.info("🎉 独立期权交易工作流程完成")
        return True

    except KeyboardInterrupt:
        logger.warning("⏹️ 期权交易被用户中断")
        return False
    except Exception as e:
        logger.error(f"💥 期权交易中出现意外错误: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="独立期权交易系统启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 纸上期权交易
  python start_options_only.py --paper --capital 100000
  
  # 实盘期权交易
  python start_options_only.py --live --capital 50000
  
  # 跳过数据下载（使用缓存）
  python start_options_only.py --paper --capital 100000 --skip-download
        """,
    )

    # 交易模式
    trading_group = parser.add_mutually_exclusive_group(required=True)
    trading_group.add_argument(
        "--paper", action="store_true", help="纸上交易模式 (IBKR端口7497)"
    )
    trading_group.add_argument(
        "--live", action="store_true", help="实盘交易模式 (IBKR端口7496)"
    )

    # 资金设置
    parser.add_argument(
        "--capital", type=float, default=100000.0, help="期权交易资金 (默认: 100000)"
    )

    # 其他选项
    parser.add_argument(
        "--skip-download", action="store_true", help="跳过数据下载，使用缓存数据"
    )

    args = parser.parse_args()

    # 确保结果目录存在
    today = datetime.now().strftime("%Y%m%d")
    os.makedirs(f"results/{today}/logs", exist_ok=True)

    # 运行独立期权交易工作流程
    try:
        success = asyncio.run(run_options_only_workflow(args))
        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n⏹️ 期权交易工作流被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
