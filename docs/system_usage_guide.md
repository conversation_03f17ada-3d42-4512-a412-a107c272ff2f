# IBKR自动交易系统使用指南

## 系统概述

本系统是一个基于Interactive Brokers (IBKR) API的自动化交易系统，集成了机器学习模型、相关性分析、期权套利和风险管理功能。

## 核心功能

### 1. 数据下载与分析
- **股票数据下载**：从IBKR获取实时和历史股票数据
- **ML模型分析**：使用机器学习模型预测股票趋势
- **相关性分析**：识别股票间的相关性和背离机会
- **期权套利分析**：基于ML验证的相关性寻找期权交易机会

### 2. 交易机器人系统
系统提供6种不同策略的交易机器人：

#### Adam机器人 (保守型)
- **策略**：买入"HIGHLY BELOW TREND"股票
- **风险控制**：
  - 最小止盈：3%
  - 最大止损：10%
- **适用场景**：保守投资，寻找被低估的股票

#### Betty机器人 (激进型) 
- **策略**：买入"HIGHLY ABOVE TREND"股票
- **风险控制**：
  - 最小止盈：10%
  - 最大止损：3%
- **适用场景**：追涨策略，捕捉强势股票

#### Chris机器人 (蓝筹股专家)
- **策略**：只买入指定的蓝筹股 (GOOGL, AMZN, AAPL, MSFT, META)
- **特点**：专注于大型科技股，分散投资
- **适用场景**：稳健投资，专注知名大盘股

#### Dany机器人 (均衡型)
- **策略**：买入"BELOW TREND"和"HIGHLY BELOW TREND"股票
- **风险控制**：
  - 最小止盈：5%
  - 最大止损：5%
- **适用场景**：均衡投资策略

#### Eddy机器人 (趋势跟随)
- **策略**：买入"ABOVE TREND"和"HIGHLY ABOVE TREND"股票
- **风险控制**：
  - 最小止盈：8%
  - 最大止损：4%
- **适用场景**：趋势跟随，捕捉上涨趋势

#### Flora机器人 (价值投资)
- **策略**：买入"EXTREME BELOW TREND"股票
- **风险控制**：
  - 最小止盈：15%
  - 最大止损：8%
- **适用场景**：价值投资，寻找极度被低估的股票

### 3. 趋势分类系统

系统使用7级趋势分类：

1. **EXTREME BELOW TREND** (极度低于趋势)：0.1-0.6%的股票
2. **HIGHLY BELOW TREND** (高度低于趋势)：1-2%的股票
3. **BELOW TREND** (低于趋势)：5-8%的股票
4. **ALONG TREND** (沿趋势)：38-79%的股票
5. **ABOVE TREND** (高于趋势)：5-8%的股票
6. **HIGHLY ABOVE TREND** (高度高于趋势)：1-2%的股票
7. **EXTREME ABOVE TREND** (极度高于趋势)：0.1-0.6%的股票

## 纸面交易 vs 真实交易

### 端口区别
- **纸上交易**：IBKR端口 7497，`--paper` 参数
- **真实交易**：IBKR端口 7496，`--live` 参数

### 配置区别
```python
# 纸上交易配置
IBKRConfig(
    port=7497,
    paper_trading=True
)

# 真实交易配置
IBKRConfig(
    port=7496,
    paper_trading=False
)
```

## 系统使用方法

### 1. 基本命令

#### 完整交易流程
```bash
# 纸上交易模式（推荐新手）
python start_full_trading.py --paper --bot betty --capital 100000

# 实盘交易模式（需谨慎）
python start_full_trading.py --live --bot adam --capital 50000

# 启用期权交易
python start_full_trading.py --paper --enable-options --bot betty --capital 100000
```

#### 更新股票池
```bash
# 更新股票列表和分类信息
python update_stock_universe.py
```

#### 单独运行各模块
```bash
# 只下载数据
python core/market_data.py

# 只运行ML分析
python stocks/trend.py --analysis-only

# 只运行期权分析
python options/arbitrage.py
```

### 2. 命令行参数详解

#### start_full_trading.py 参数
- `--paper` / `--live`：交易模式选择
- `--bot {adam,betty,chris,dany,eddy,flora}`：选择交易机器人
- `--capital AMOUNT`：设置初始资金
- `--enable-options`：启用期权交易
- `--update-universe`：运行前更新股票池
- `--max-stocks N`：限制分析的股票数量
- `--training-years N`：设置ML模型训练年数

#### 示例命令组合
```bash
# 保守策略：Adam机器人，小资金测试
python start_full_trading.py --paper --bot adam --capital 10000

# 激进策略：Betty机器人，启用期权
python start_full_trading.py --paper --bot betty --capital 100000 --enable-options

# 蓝筹股策略：Chris机器人，大资金
python start_full_trading.py --paper --bot chris --capital 500000

# 完整流程：更新股票池 + 期权交易
python start_full_trading.py --paper --bot eddy --capital 200000 --enable-options --update-universe
```

### 3. 配置文件设置

#### IBKR连接配置 (core/config.py)
```python
@dataclass
class IBKRConfig:
    host: str = '127.0.0.1'
    port: int = 7497  # 7497=纸上交易，7496=实盘交易
    client_id: Optional[int] = None  # 自动分配
    timeout: int = 30
    paper_trading: bool = True
    max_position_size: float = 10000.0
    max_daily_trades: int = 50
    stop_loss_pct: float = 0.05
    take_profit_pct: float = 0.10
```

#### 环境变量配置
```bash
# 设置IBKR连接参数
export IBKR_HOST=127.0.0.1
export IBKR_PORT=7497
export IBKR_PAPER_TRADING=True
export IBKR_MAX_POSITION_SIZE=10000.0
```

## 风险管理

### 1. 内置风险控制
- **仓位限制**：单个股票最大仓位5%
- **止损止盈**：每个机器人都有预设的止损止盈比例
- **日交易限制**：每日最大交易次数限制
- **板块暴露**：单板块最大暴露30%

### 2. 安全建议
1. **从纸上交易开始**：新用户必须先使用`--paper`模式
2. **小资金测试**：实盘交易前用小额资金测试
3. **监控日志**：定期检查`results/YYYYMMDD/logs/`目录下的日志
4. **备份配置**：重要配置文件要备份

## 故障排除

### 1. 常见问题

#### IBKR连接失败
```
错误：客户号码已被使用，无法连接
解决：系统会自动重试不同的客户端ID，等待几分钟后重试
```

#### 数据下载失败
```
错误：IBKR数据农场不活跃
解决：在交易时间内运行，或等待数据农场恢复
```

#### 模型训练失败
```
错误：数据不足，无法训练模型
解决：增加训练年数或减少股票数量
```

### 2. 日志文件位置
- **主日志**：`results/YYYYMMDD/logs/trading_system.log`
- **IBKR日志**：`results/YYYYMMDD/logs/ibkr_trading.log`
- **错误日志**：`results/YYYYMMDD/logs/error.log`

### 3. 结果文件
- **交易结果**：`results/YYYYMMDD/trading_results_YYYYMMDD.csv`
- **相关性分析**：`results/YYYYMMDD/correlation_analysis_YYYYMMDD.csv`
- **期权机会**：`results/YYYYMMDD/options_opportunities_YYYYMMDD.csv`

## 期权交易详细说明

### 1. 期权交易逻辑

#### 核心策略：相关性背离套利
期权交易基于**机器学习验证的股票相关性背离**进行套利：

1. **相关性识别**：ML模型识别高相关性股票对
2. **背离检测**：当相关股票价格出现显著背离时
3. **期权选择**：在背离股票上买入期权，等待价格回归
4. **风险控制**：严格的流动性、时间和风险管理

#### 期权选择标准
```python
# 流动性要求
min_daily_volume: 1500手
min_open_interest: 150手
max_bid_ask_spread: 10%

# 时间管理
min_time_to_expiry: 15天
optimal_time_to_expiry: 22天
max_time_to_expiry: 30天

# 价位选择
optimal_moneyness: 4%价外 (轻度价外)
max_moneyness_deviation: 3%
```

### 2. 期权交易命令

#### 方式一：完整交易系统（股票 + 期权）
使用 `--bot` 参数时，系统执行**股票交易 + 期权交易**：

```bash
# 股票交易（Betty机器人）+ 期权交易
python start_full_trading.py --paper --enable-options --bot betty --capital 100000

# 完整流程（更新股票池 + 股票交易 + 期权交易）
python start_full_trading.py --paper --enable-options --update-universe --bot betty --capital 100000

# 实盘完整交易（谨慎使用）
python start_full_trading.py --live --enable-options --bot betty --capital 100000
```

#### 方式二：独立期权交易（仅期权）
使用独立脚本时，系统**只执行期权交易**：

```bash
# 纸上独立期权交易
python start_options_only.py --paper --capital 100000

# 实盘独立期权交易
python start_options_only.py --live --capital 50000

# 跳过数据下载（使用缓存）
python start_options_only.py --paper --capital 100000 --skip-download
```

#### 方式三：期权监控和管理
```bash
# 监控现有期权仓位
python options/monitor.py --paper

# 自动出场监控
python options/monitor.py --paper --auto-exit

# 持续监控（每5分钟检查）
python options/monitor.py --paper --continuous --interval 300
```

### 3. 机器人期权偏好

#### 重要说明
**所有机器人共享相同的期权策略**，期权交易是独立于股票交易的套利系统：

- **期权交易**：基于ML相关性背离，与机器人股票策略无关
- **股票交易**：每个机器人有不同的买入信号和风险偏好
- **资金分配**：期权交易最多使用8%的总资金

#### 期权交易流程
1. **相关性分析**：ML模型识别所有股票的相关性
2. **背离检测**：找出价格显著背离的相关股票对
3. **期权筛选**：在背离股票上寻找符合条件的期权
4. **风险评估**：评估期权的流动性、时间价值和预期收益
5. **执行交易**：买入最优期权合约

### 4. 期权风险管理

#### 资金限制
- **最大分配**：总资金的8%
- **单策略风险**：1.5%
- **单板块暴露**：4%

#### 止损规则
- **期权止损**：50%
- **时间止损**：距离到期7天
- **相关性破裂**：相关系数低于0.3

#### 流动性要求
- **最小日交易量**：1500手
- **最小未平仓**：150手
- **最大价差**：10%
- **最小期权价格**：$0.75

## 系统架构

### 1. 核心模块
- `core/`：核心功能（IBKR客户端、机器人、配置）
- `stocks/`：股票分析和趋势预测
- `options/`：期权套利分析
- `data/`：数据存储和缓存

### 2. 工作流程
1. **数据下载**：从IBKR获取股票数据
2. **ML分析**：运行机器学习模型预测趋势
3. **相关性分析**：识别股票相关性和背离
4. **交易执行**：根据机器人策略执行交易
5. **期权分析**：寻找期权套利机会（如果启用）
6. **风险管理**：监控仓位并执行止损止盈

## 最佳实践

### 1. 新用户建议
1. 先阅读本文档了解系统功能
2. 使用纸上交易模式熟悉系统
3. 从保守的Adam机器人开始
4. 逐步增加资金和复杂度

### 2. 高级用户建议
1. 组合使用多个机器人
2. 根据市场条件调整策略
3. 定期分析交易结果
4. 优化风险管理参数

### 3. 监控建议
1. 每日检查交易结果
2. 监控系统日志
3. 定期备份重要数据
4. 关注市场异常情况

## 技术支持

如遇到问题，请：
1. 检查日志文件中的错误信息
2. 确认IBKR Gateway/TWS正常运行
3. 验证网络连接和权限设置
4. 参考故障排除章节

---

**免责声明**：本系统仅供学习和研究使用。实盘交易存在风险，请谨慎使用并自行承担风险。
