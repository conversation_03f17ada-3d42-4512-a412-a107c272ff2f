#!/usr/bin/env python
"""每日交易系统主入口

智能股票交易系统的核心执行模块，集成机器学习模型分析、相关性计算和交易决策。
工作流程：模型分析 -> 相关性分析 -> 交易决策。提供多种ML模型（<PERSON>、<PERSON>、<PERSON>、
<PERSON><PERSON>、<PERSON>、<PERSON>）进行股票趋势预测和相关性背离分析。
"""

import argparse
import asyncio
import logging
import os
import pickle
import shutil
from datetime import datetime
from typing import Dict

import numpy as np
import pandas as pd

from core.bots import <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>
from core.broker import IBKRClient
from core.config import DEFAULT_CONFIG, IBKRConfig
from core.market_data import download
from core.models import train_msis_mcs
from core.stats import estimate_logprice_statistics, estimate_price_statistics
from core.utils import extract_hierarchical_info
from services.updater import StockUniverseUpdater
from stocks.portfolio import PortfolioManager

# Setup logging - will be reconfigured in DailyTradingSystem.__init__
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DailyTradingSystem:
    """
    Daily Trading System following the correct workflow:
    1. Download all stock data
    2. Run ML model to identify trends
    3. Perform correlation analysis
    4. Execute trades based on correlation results
    """

    def __init__(
        self,
        config: IBKRConfig = None,
        bot_class=Adam,
        initial_capital: float = 10000.0,
        update_universe: bool = False,
        extreme_level: str = "high",
        skip_download: bool = False,
        enable_options: bool = False,
    ):
        self.config = config or DEFAULT_CONFIG
        # Always enable real trading - IBKR port determines paper vs live mode
        self.bot = bot_class(
            initial_capital, real_trading=True, ibkr_config=self.config
        )
        self.ibkr_client = IBKRClient(self.config)
        self.update_universe = update_universe
        self.extreme_level = extreme_level
        self.skip_download = skip_download
        self.enable_options = enable_options

        # Test mode attributes
        self.test_mode = False
        self.test_symbols = None

        # Clean old cache files on startup
        self._cleanup_old_cache_files()

        # System state
        self.all_stocks_data = None
        self.model_results = None
        self.correlation_analysis = None
        self.trading_decisions = None
        self.portfolio_manager = None

        # Create results directory structure
        self._setup_results_directories()

        # File paths - use data/cache for all data files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.model_cache_file = f"data/cache/temp/daily_model_cache_{timestamp}.pickle"

        # Results files go in organized directories
        date_str = datetime.now().strftime("%Y%m%d")
        self.results_file = f"results/{date_str}/daily_results_{date_str}.csv"
        self.charts_dir = f"results/{date_str}/charts"
        self.options_charts_dir = f"results/{date_str}/options_charts"
        self.logs_dir = f"results/{date_str}/logs"

        # Setup organized logging
        self._setup_logging()

    def _cleanup_old_cache_files(self):
        """清理旧缓存文件以防止冲突"""
        try:
            # Remove old model cache files (keep only current session)
            for filename in os.listdir("."):
                if filename.startswith("daily_model_cache_") and filename.endswith(
                    ".pickle"
                ):
                    if hasattr(
                        self, "model_cache_file"
                    ) and filename != os.path.basename(self.model_cache_file):
                        os.remove(filename)
                        logger.info(f"Removed old cache file: {filename}")

                # Also remove generic cache files that might cause conflicts
                if filename in ["daily_model_cache.pickle"]:
                    os.remove(filename)
                    logger.info(f"Removed conflicting cache file: {filename}")

        except Exception as e:
            logger.warning(f"Cache cleanup warning: {e}")

    def _setup_results_directories(self):
        """为结果、图表和数据设置有组织的目录结构"""
        try:
            date_str = datetime.now().strftime("%Y%m%d")

            # Create main directories
            main_dirs = ["results"]
            for main_dir in main_dirs:
                if not os.path.exists(main_dir):
                    os.makedirs(main_dir)
                    logger.info(f"📁 Created main directory: {main_dir}")

            # Create date-specific directory
            date_dir = f"results/{date_str}"
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                logger.info(f"📁 Created date directory: {date_dir}")

            # Create subdirectories for different types of files
            subdirs = ["charts", "options_charts", "logs"]
            for subdir in subdirs:
                subdir_path = f"{date_dir}/{subdir}"
                if not os.path.exists(subdir_path):
                    os.makedirs(subdir_path)
                    logger.info(f"📁 Created subdirectory: {subdir_path}")

        except Exception as e:
            logger.warning(f"Directory setup warning: {e}")

    def _setup_logging(self):
        """设置有组织的日志记录到results/date/logs目录"""
        try:
            # Create log file path
            date_str = datetime.now().strftime("%Y%m%d")
            log_file = f"results/{date_str}/logs/ibkr_trading.log"

            # Configure file handler
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            file_handler.setFormatter(file_formatter)

            # Configure console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter(
                "%(asctime)s - %(levelname)s - %(message)s"
            )
            console_handler.setFormatter(console_formatter)

            # Get root logger and configure
            root_logger = logging.getLogger()
            root_logger.handlers.clear()  # Clear existing handlers
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            root_logger.setLevel(logging.INFO)

            logger.info(f"📝 Logging configured: {log_file}")

        except Exception as e:
            logger.warning(f"Logging setup warning: {e}")

    def clean_cache_files(self):
        """清理缓存文件以强制使用优化顺序方法进行新下载"""
        import glob

        # Clean all cache and data files from data/cache
        cache_patterns = [
            "data/cache/temp/*.pkl",
            "data/cache/temp/*.pickle",
            "data/cache/temp/*.json",
            "incremental_data.pkl",  # Legacy files in root
            "model_cache_*.pkl",  # Legacy files in root
            "daily_model_cache_*.pickle",  # Legacy files in root
            "data.pickle",  # Legacy files in root
            "download_progress.json",  # Legacy files in root
        ]

        logger.info("🧹 Cleaning cache files for optimized sequential download...")

        removed_count = 0
        for pattern in cache_patterns:
            if "*" in pattern:
                for file in glob.glob(pattern):
                    if os.path.exists(file):
                        os.remove(file)
                        logger.info(f"   Removed: {file}")
                        removed_count += 1
            else:
                if os.path.exists(pattern):
                    os.remove(pattern)
                    logger.info(f"   Removed: {pattern}")
                    removed_count += 1

        logger.info(
            f"✅ Cache cleaned - removed {removed_count} files. System will use fresh download."
        )

    def clean_invalid_symbols(self):
        """根据下载进度从 symbols_list.txt 中移除无效代码"""
        try:
            import json
            import os

            progress_file = "download_progress.json"
            if not os.path.exists(progress_file):
                logger.info("No progress file found - nothing to clean")
                return

            # Load progress data
            with open(progress_file, "r") as f:
                progress_data = json.load(f)

            invalid_symbols = set(progress_data.get("invalid_symbols", []))

            if not invalid_symbols:
                logger.info("No invalid symbols found")
                return

            # Read current symbols list
            symbols_file = "symbols_list.txt"
            if not os.path.exists(symbols_file):
                logger.warning("symbols_list.txt not found")
                return

            with open(symbols_file, "r") as f:
                all_symbols = [line.strip() for line in f if line.strip()]

            # Remove invalid symbols
            original_count = len(all_symbols)
            cleaned_symbols = [s for s in all_symbols if s not in invalid_symbols]
            removed_count = original_count - len(cleaned_symbols)

            if removed_count > 0:
                # Backup original file
                backup_file = f"symbols_list_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                shutil.copy2(symbols_file, backup_file)
                logger.info(f"📁 Backed up original symbols list to {backup_file}")

                # Write cleaned symbols list
                with open(symbols_file, "w") as f:
                    for symbol in cleaned_symbols:
                        f.write(f"{symbol}\n")

                logger.info(
                    f"🧹 Removed {removed_count} invalid symbols from symbols list"
                )
                logger.info(f"📊 Symbols: {original_count} → {len(cleaned_symbols)}")

                # Show some examples
                removed_examples = sorted(list(invalid_symbols))[:10]
                logger.info(
                    f"🚫 Removed symbols (examples): {', '.join(removed_examples)}"
                )
                if len(invalid_symbols) > 10:
                    logger.info(f"   ... and {len(invalid_symbols) - 10} more")
            else:
                logger.info("✅ No invalid symbols to remove")

        except Exception as e:
            logger.error(f"Error cleaning invalid symbols: {e}")

    async def step0_update_stock_universe(self) -> bool:
        """步骤0: 如果要求，更新股票全集"""
        if not self.update_universe:
            logger.info(
                "Skipping stock universe update (use --update-universe to enable)"
            )
            return True

        logger.info("Step 0: Updating stock universe...")

        try:
            updater = StockUniverseUpdater(self.config)
            await updater.run_full_update()

            logger.info("Stock universe update completed successfully")
            return True

        except Exception as e:
            logger.error(f"Error in step 0: {e}")
            return False

    async def step1_download_all_stocks(self) -> bool:
        """步骤1: 下载所有可用股票的数据"""
        if self.skip_download:
            logger.info("Step 1: Skipping download - using existing cached CSV data...")
            # 优先加载pickle文件中的完整数据，然后才是CSV文件
            try:
                import pickle

                import pandas as pd

                cache_dir = "cache/stock_data"

                # 首先尝试加载incremental_data.pkl（包含所有下载的股票）
                incremental_file = os.path.join(cache_dir, "incremental_data.pkl")
                if os.path.exists(incremental_file):
                    logger.info(
                        "🔄 Loading comprehensive stock data from incremental_data.pkl..."
                    )
                    try:
                        with open(incremental_file, "rb") as f:
                            incremental_data = pickle.load(f)

                        if (
                            incremental_data
                            and "tickers" in incremental_data
                            and len(incremental_data["tickers"]) > 0
                        ):
                            # 加载sector和industry信息
                            sectors = {}
                            industries = {}
                            stock_info = self._load_stock_info_from_csv()

                            for ticker in incremental_data["tickers"]:
                                if ticker in stock_info:
                                    sectors[ticker] = stock_info[ticker]["sector"]
                                    industries[ticker] = stock_info[ticker]["industry"]
                                else:
                                    sectors[ticker] = "Unknown"
                                    industries[ticker] = "Unknown"

                            # 添加sector和industry信息到数据结构
                            incremental_data["sectors"] = sectors
                            incremental_data["industries"] = industries

                            self.all_stocks_data = incremental_data
                            logger.info(
                                f"✅ Successfully loaded comprehensive data for {len(incremental_data['tickers'])} stocks from pickle file"
                            )
                            return True
                    except Exception as e:
                        logger.warning(f"Failed to load incremental data: {e}")

                # 如果pickle文件不可用，回退到CSV文件
                if os.path.exists(cache_dir):
                    csv_files = [f for f in os.listdir(cache_dir) if f.endswith(".csv")]

                    if csv_files:
                        # Load data from CSV files
                        tickers = []
                        dates = []
                        prices = []
                        volumes = []

                        for csv_file in csv_files:
                            symbol = csv_file.replace(".csv", "")
                            file_path = os.path.join(cache_dir, csv_file)

                            try:
                                df = pd.read_csv(file_path)
                                if len(df) > 0:
                                    tickers.append(symbol)
                                    # Convert to expected format
                                    stock_dates = (
                                        pd.to_datetime(df["date"])
                                        .dt.strftime("%Y-%m-%d")
                                        .tolist()
                                    )
                                    stock_prices = df["close"].tolist()
                                    stock_volumes = df["volume"].tolist()

                                    dates.append(stock_dates)
                                    prices.append(stock_prices)
                                    volumes.append(stock_volumes)

                                    logger.info(
                                        f"✅ Loaded {len(df)} days of data for {symbol}"
                                    )
                            except Exception as e:
                                logger.warning(f"Failed to load {csv_file}: {e}")

                        if tickers:
                            # Load sector and industry information
                            sectors = {}
                            industries = {}

                            try:
                                stock_info_file = "data/stock_info/stock_info.csv"
                                if os.path.exists(stock_info_file):
                                    stock_info_df = pd.read_csv(stock_info_file)
                                    for _, row in stock_info_df.iterrows():
                                        symbol = row["symbol"]
                                        if symbol in tickers:
                                            sectors[symbol] = row.get(
                                                "sector", "Unknown"
                                            )
                                            industries[symbol] = row.get(
                                                "industry", "Unknown"
                                            )
                                    logger.info(
                                        f"✅ Loaded sector/industry info for {len(sectors)} stocks"
                                    )
                                else:
                                    logger.warning(
                                        "stock_info.csv not found, using default sectors"
                                    )
                                    for ticker in tickers:
                                        sectors[ticker] = "Technology"
                                        industries[ticker] = "Software"
                            except Exception as e:
                                logger.warning(f"Failed to load sector info: {e}")
                                for ticker in tickers:
                                    sectors[ticker] = "Technology"
                                    industries[ticker] = "Software"

                            # Create data structure expected by the system
                            self.all_stocks_data = {
                                "tickers": tickers,
                                "dates": dates,
                                "price": prices,
                                "volume": volumes,
                                "currencies": ["USD"] * len(tickers),
                                "sectors": sectors,
                                "industries": industries,
                                "exchange_rates": {
                                    "USD": [1.0] * len(dates[0]) if dates else [1.0]
                                },
                                "default_currency": "USD",
                            }

                            logger.info(
                                f"✅ Successfully loaded cached data for {len(tickers)} stocks"
                            )
                            return True
                        else:
                            logger.warning("No valid CSV data found")
                    else:
                        logger.warning("No CSV files found in cache directory")
                else:
                    logger.warning("Cache directory not found")
            except Exception as e:
                logger.warning(f"Failed to load cached CSV data: {e}")

            # If no cached data, create minimal test data
            logger.info("Creating minimal test data for system testing...")
            test_symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
            from datetime import datetime, timedelta

            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            start_str = start_date.strftime("%Y-%m-%d")
            end_str = end_date.strftime("%Y-%m-%d")

            self.all_stocks_data = download(test_symbols, start=start_str, end=end_str)
            if self.all_stocks_data and len(self.all_stocks_data["tickers"]) > 0:
                logger.info(
                    f"✅ Created test data for {len(self.all_stocks_data['tickers'])} stocks"
                )
                return True
            else:
                logger.error("Failed to create test data")
                return False

        logger.info("Step 1: Downloading data for all stocks...")

        try:
            # Load symbols based on mode
            if self.test_mode and self.test_symbols:
                all_symbols = self.test_symbols
                logger.info(f"🧪 TEST MODE: Using {len(all_symbols)} test symbols")
            else:
                # Load all available symbols
                with open("data/stock_info/symbols_list.txt", "r") as f:
                    all_symbols = f.read().strip().split()

                logger.info(f"Found {len(all_symbols)} symbols to analyze")

                # Download data for all symbols - NO LIMITS!
                # This is crucial for finding correlations across the entire market
                logger.info(
                    f"Downloading data for ALL {len(all_symbols)} symbols to maximize correlation opportunities"
                )

            # Calculate date range for sufficient historical data
            from datetime import datetime, timedelta

            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)  # Get 1 year of data

            start_str = start_date.strftime("%Y-%m-%d")
            end_str = end_date.strftime("%Y-%m-%d")

            logger.info(f"Downloading data from {start_str} to {end_str}")

            # Use our own IBKR client to maintain connection throughout the workflow
            from core.market_data import IBKRDataProvider

            data_provider = IBKRDataProvider(self.config)

            try:
                # Connect using our data provider
                if not await data_provider.connect():
                    logger.error("Failed to connect to IBKR for data download")
                    return False

                # Download data while keeping connection alive
                self.all_stocks_data = await data_provider.download_multiple_stocks(
                    all_symbols, start_str, end_str, max_concurrent=1
                )

                # Keep the connection alive by transferring it to our main client
                if data_provider.connected:
                    try:
                        # Verify the connection is still active before transferring
                        if data_provider.ibkr_client.ib.isConnected():
                            # Transfer the connection to our main IBKR client
                            self.ibkr_client.ib = data_provider.ibkr_client.ib
                            self.ibkr_client.connected = True
                            # Don't disconnect the data provider's client
                            data_provider.connected = False
                            logger.info(
                                "✅ IBKR connection transferred to main client - keeping alive for trading"
                            )
                        else:
                            logger.warning(
                                "⚠️ Data provider connection is not active, cannot transfer"
                            )
                            await data_provider.disconnect()
                    except Exception as e:
                        logger.error(f"❌ Failed to transfer IBKR connection: {e}")
                        await data_provider.disconnect()

            except Exception as e:
                logger.error(f"Data download failed: {e}")
                if data_provider.connected:
                    await data_provider.disconnect()
                return False

            if not self.all_stocks_data or len(self.all_stocks_data["tickers"]) == 0:
                logger.error("Failed to download stock data")
                return False

            logger.info(
                f"Successfully downloaded data for {len(self.all_stocks_data['tickers'])} stocks"
            )
            logger.info(
                f"Data period: {self.all_stocks_data['dates'][0]} to {self.all_stocks_data['dates'][-1]}"
            )

            return True

        except Exception as e:
            logger.error(f"Error in step 1: {e}")
            return False

    def step2_run_model_analysis(self) -> bool:
        """步骤2: 运行ML模型识别趋势上方/下方的股票"""
        logger.info("Step 2: Running ML model analysis...")

        try:
            if not self.all_stocks_data:
                logger.error("No stock data available for model analysis")
                return False

            # Prepare data for model - handle invalid values
            prices = self.all_stocks_data["price"]

            # Handle case where prices is a list (due to inhomogeneous data)
            if isinstance(prices, list):
                logger.warning("Price data is a list (inhomogeneous shapes detected)")
                logger.info(f"Number of stocks: {len(prices)}")

                # Find stocks with sufficient data for meaningful analysis
                if len(prices) > 0:
                    lengths = [len(p) if hasattr(p, "__len__") else 0 for p in prices]
                    min_length = min(lengths) if lengths else 0
                    max_length = max(lengths) if lengths else 0
                    logger.info(
                        f"Price array lengths: min={min_length}, max={max_length}"
                    )

                    # Set minimum required data points for scientific analysis
                    min_required_days = 60  # Require at least 60 trading days (~3 months) - 进一步降低要求以包含更多股票

                    if max_length < min_required_days:
                        logger.error(
                            f"Insufficient data: maximum length {max_length} < required {min_required_days}"
                        )
                        return False

                    # Only use stocks with sufficient data
                    sufficient_data_stocks = []
                    valid_tickers = []
                    valid_sectors = {}
                    valid_industries = {}

                    for i, price_array in enumerate(prices):
                        if (
                            hasattr(price_array, "__len__")
                            and len(price_array) >= min_required_days
                        ):
                            # Take the last min_required_days values (most recent data)
                            standardized_array = np.array(
                                price_array[-min_required_days:]
                            )
                            if np.any(np.isfinite(standardized_array)) and np.any(
                                standardized_array > 0
                            ):
                                sufficient_data_stocks.append(standardized_array)
                                ticker = self.all_stocks_data["tickers"][i]
                                valid_tickers.append(ticker)
                                if ticker in self.all_stocks_data["sectors"]:
                                    valid_sectors[ticker] = self.all_stocks_data[
                                        "sectors"
                                    ][ticker]
                                if ticker in self.all_stocks_data["industries"]:
                                    valid_industries[ticker] = self.all_stocks_data[
                                        "industries"
                                    ][ticker]

                    if len(sufficient_data_stocks) == 0:
                        logger.error("No stocks with sufficient data for analysis")
                        return False

                    # Convert to numpy array
                    prices = np.array(sufficient_data_stocks)

                    # Update the data structure
                    self.all_stocks_data["price"] = prices
                    self.all_stocks_data["tickers"] = valid_tickers
                    self.all_stocks_data["sectors"] = valid_sectors
                    self.all_stocks_data["industries"] = valid_industries

                    rejected_count = (
                        len(self.all_stocks_data["tickers"]) - len(valid_tickers)
                        if "tickers" in self.all_stocks_data
                        else 0
                    )
                    logger.info(
                        f"✅ Selected {len(sufficient_data_stocks)} stocks with sufficient data ({min_required_days} days each)"
                    )
                    logger.info(
                        f"❌ Rejected {rejected_count} stocks with insufficient data (< {min_required_days} days)"
                    )
                    logger.info(
                        "📊 Data quality: Using only stocks with robust historical data for scientific analysis"
                    )
                else:
                    logger.error("No price data available")
                    return False

            # Convert to numpy array if not already
            if not isinstance(prices, np.ndarray):
                prices = np.array(prices)

            logger.info(f"Initial data shape: {prices.shape}")
            logger.info(
                f"Price data range: min={np.nanmin(prices):.4f}, max={np.nanmax(prices):.4f}"
            )
            logger.info(
                f"NaN count: {np.sum(np.isnan(prices))}, Zero count: {np.sum(prices == 0)}"
            )

            # Advanced data cleaning and validation
            logger.info("Performing advanced data cleaning...")

            # Step 1: Clean extreme outliers and invalid values
            cleaned_prices = prices.copy()

            # Remove negative prices and extreme outliers
            for i in range(len(cleaned_prices)):
                stock_prices = cleaned_prices[i]

                # Replace negative prices with NaN
                stock_prices[stock_prices <= 0] = np.nan

                # Remove extreme outliers (prices > $10,000 are likely errors)
                stock_prices[stock_prices > 10000] = np.nan

                # Forward fill small gaps (up to 5 consecutive NaN values)
                mask = np.isnan(stock_prices)
                if np.any(~mask):  # If there's at least some valid data
                    # Forward fill
                    last_valid = None
                    gap_count = 0
                    for j in range(len(stock_prices)):
                        if not mask[j]:
                            last_valid = stock_prices[j]
                            gap_count = 0
                        elif last_valid is not None and gap_count < 5:
                            stock_prices[j] = last_valid
                            gap_count += 1
                        else:
                            gap_count += 1

                cleaned_prices[i] = stock_prices

            # Step 2: Progressive validation criteria based on data quality
            total_nan_ratio = np.sum(np.isnan(cleaned_prices)) / cleaned_prices.size

            if total_nan_ratio > 0.4:  # If >40% data is missing, be very lenient
                min_valid_ratio = 0.3  # 降低到30%以包含更多股票
                min_recent_ratio = 0.2
                logger.warning(
                    f"High missing data rate ({total_nan_ratio:.1%}), using lenient validation"
                )
            elif (
                total_nan_ratio > 0.2
            ):  # If >20% data is missing, be moderately lenient
                min_valid_ratio = 0.4  # 降低到40%
                min_recent_ratio = 0.25
                logger.info(
                    f"Moderate missing data rate ({total_nan_ratio:.1%}), using moderate validation"
                )
            else:  # Good data quality, use stricter validation
                min_valid_ratio = 0.6  # 降低到60%
                min_recent_ratio = 0.4  # 降低到40%
                logger.info(
                    f"Good data quality ({total_nan_ratio:.1%}), using relaxed validation for more stocks"
                )

            recent_days = 20

            valid_mask = []
            for i in range(len(cleaned_prices)):
                stock_prices = cleaned_prices[i]
                ticker = self.all_stocks_data["tickers"][i]

                # Check overall validity
                valid_points = np.sum(np.isfinite(stock_prices) & (stock_prices > 0))
                valid_ratio = valid_points / len(stock_prices)

                # Check recent data validity
                recent_prices = stock_prices[-recent_days:]
                recent_valid = np.sum(np.isfinite(recent_prices) & (recent_prices > 0))
                recent_ratio = recent_valid / len(recent_prices)

                # Check if stock has reasonable price range
                if valid_points > 0:
                    valid_data = stock_prices[
                        np.isfinite(stock_prices) & (stock_prices > 0)
                    ]
                    price_range = np.max(valid_data) / np.min(valid_data)
                    reasonable_range = (
                        price_range < 100
                    )  # Price shouldn't vary more than 100x
                else:
                    reasonable_range = False

                # More lenient criteria
                is_valid = (
                    (valid_ratio >= min_valid_ratio)
                    and (recent_ratio >= min_recent_ratio)
                    and reasonable_range
                )

                if not is_valid:
                    logger.debug(
                        f"Rejected {ticker}: valid_ratio={valid_ratio:.2f}, recent_ratio={recent_ratio:.2f}, reasonable_range={reasonable_range}"
                    )

                valid_mask.append(is_valid)

            valid_mask = np.array(valid_mask)

            logger.info("Data cleaning results:")
            logger.info(f"  Original stocks: {len(prices)}")
            logger.info(f"  Valid stocks after cleaning: {np.sum(valid_mask)}")
            logger.info(
                f"  Rejection rate: {(1 - np.sum(valid_mask) / len(prices)) * 100:.1f}%"
            )

            if not np.any(valid_mask):
                logger.error("No stocks with valid price data")
                logger.error(
                    f"Checked {len(prices)} stocks, none met validity criteria"
                )

                # Debug: show some sample data
                logger.error("Sample data analysis:")
                for i in range(min(5, len(prices))):
                    stock_prices = prices[i]
                    ticker = self.all_stocks_data["tickers"][i]
                    valid_points = np.sum(
                        (stock_prices > 0) & np.isfinite(stock_prices)
                    )
                    valid_ratio = valid_points / len(stock_prices)
                    recent_prices = stock_prices[-10:]
                    recent_valid = np.sum(
                        (recent_prices > 0) & np.isfinite(recent_prices)
                    )
                    recent_ratio = recent_valid / len(recent_prices)

                    logger.error(
                        f"  {ticker}: {valid_ratio:.1%} valid, recent {recent_ratio:.1%}, shape {stock_prices.shape}"
                    )
                    logger.error(f"    Recent values: {recent_prices}")

                return False

            # Filter data to only valid stocks
            valid_prices = cleaned_prices[valid_mask]
            valid_tickers = [
                self.all_stocks_data["tickers"][i]
                for i in range(len(valid_mask))
                if valid_mask[i]
            ]
            valid_sectors = {
                ticker: self.all_stocks_data["sectors"][ticker]
                for ticker in valid_tickers
            }
            valid_industries = {
                ticker: self.all_stocks_data["industries"][ticker]
                for ticker in valid_tickers
            }

            logger.info(
                f"Filtered to {len(valid_tickers)} stocks with valid price data (from {len(self.all_stocks_data['tickers'])})"
            )

            # Final data cleaning for model input
            final_prices = []
            final_tickers = []
            final_sectors = {}
            final_industries = {}

            for i, ticker in enumerate(valid_tickers):
                stock_prices = valid_prices[i]

                # Ensure no NaN or invalid values remain
                if np.all(np.isfinite(stock_prices)) and np.all(stock_prices > 0):
                    final_prices.append(stock_prices)
                    final_tickers.append(ticker)
                    final_sectors[ticker] = valid_sectors[ticker]
                    final_industries[ticker] = valid_industries[ticker]
                else:
                    logger.debug(
                        f"Final rejection of {ticker}: contains NaN or invalid values"
                    )

            if len(final_prices) == 0:
                logger.error("No stocks passed final validation")
                return False

            # Ensure minimum number of stocks for meaningful analysis
            min_stocks_required = 10  # 进一步降低最小要求以适应更多市场情况
            if len(final_prices) < min_stocks_required:
                logger.warning(
                    f"Only {len(final_prices)} stocks passed validation (minimum: {min_stocks_required})"
                )
                logger.warning(
                    "Relaxing validation criteria to ensure sufficient data..."
                )

                # Emergency fallback: use top stocks by data completeness
                stock_scores = []
                for i, ticker in enumerate(valid_tickers):
                    stock_prices = valid_prices[i]
                    valid_points = np.sum(
                        np.isfinite(stock_prices) & (stock_prices > 0)
                    )
                    completeness = valid_points / len(stock_prices)
                    stock_scores.append((completeness, ticker, i))

                # Sort by completeness and take top stocks
                stock_scores.sort(reverse=True)
                emergency_count = min(min_stocks_required, len(stock_scores))

                final_prices = []
                final_tickers = []
                final_sectors = {}
                final_industries = {}

                for completeness, ticker, i in stock_scores[:emergency_count]:
                    stock_prices = valid_prices[i]
                    # Fill remaining NaN with forward fill
                    mask = np.isnan(stock_prices)
                    if np.any(~mask):
                        # Forward fill all NaN values
                        last_valid = None
                        for j in range(len(stock_prices)):
                            if not mask[j]:
                                last_valid = stock_prices[j]
                            elif last_valid is not None:
                                stock_prices[j] = last_valid

                        # If still has NaN at the beginning, backward fill
                        for j in range(len(stock_prices) - 1, -1, -1):
                            if not np.isnan(stock_prices[j]):
                                break
                        if j < len(stock_prices) - 1:
                            stock_prices[: j + 1] = stock_prices[j]

                        # Final check
                        if np.all(np.isfinite(stock_prices)) and np.all(
                            stock_prices > 0
                        ):
                            final_prices.append(stock_prices)
                            final_tickers.append(ticker)
                            final_sectors[ticker] = valid_sectors[ticker]
                            final_industries[ticker] = valid_industries[ticker]

                logger.info(f"Emergency fallback: recovered {len(final_prices)} stocks")

            if len(final_prices) == 0:
                logger.error("Even emergency fallback failed - no usable data")
                return False

            final_prices = np.array(final_prices)

            logger.info(
                f"Final dataset: {len(final_tickers)} stocks ready for analysis"
            )

            # Update the data structure
            self.all_stocks_data["price"] = final_prices
            self.all_stocks_data["tickers"] = final_tickers
            self.all_stocks_data["sectors"] = final_sectors
            self.all_stocks_data["industries"] = final_industries

            logp = np.log(final_prices)
            num_stocks, t = logp.shape

            logger.info(f"Training model with {num_stocks} stocks, {t} time periods")

            # Extract hierarchical information
            info = extract_hierarchical_info(
                self.all_stocks_data["sectors"], self.all_stocks_data["industries"]
            )

            # Verify data consistency
            logger.info("Data consistency check:")
            logger.info(f"  Stocks in price data: {num_stocks}")
            logger.info(f"  Stocks in hierarchical info: {info['num_stocks']}")
            logger.info(
                f"  Sectors dict length: {len(self.all_stocks_data['sectors'])}"
            )
            logger.info(
                f"  Industries dict length: {len(self.all_stocks_data['industries'])}"
            )

            # Handle data consistency - use intersection of available data
            if info["num_stocks"] != num_stocks:
                logger.warning(
                    f"Data mismatch: price data has {num_stocks} stocks but hierarchical info has {info['num_stocks']}"
                )
                logger.info("Filtering to use only stocks with complete information...")

                # Get stocks that have both price data and hierarchical info
                price_tickers = set(self.all_stocks_data["tickers"])
                sector_tickers = set(self.all_stocks_data["sectors"].keys())
                industry_tickers = set(self.all_stocks_data["industries"].keys())

                # Find intersection - stocks with complete data
                valid_tickers = price_tickers & sector_tickers & industry_tickers
                valid_tickers = list(valid_tickers)

                logger.info(f"Found {len(valid_tickers)} stocks with complete data")

                if len(valid_tickers) < 50:
                    logger.error(
                        f"Too few stocks with complete data: {len(valid_tickers)}"
                    )
                    return False

                # Filter all data to only include valid tickers
                ticker_indices = [
                    i
                    for i, ticker in enumerate(self.all_stocks_data["tickers"])
                    if ticker in valid_tickers
                ]

                # Update price data using final_prices
                filtered_final_prices = final_prices[ticker_indices, :]

                # Update tickers list
                filtered_tickers = [
                    self.all_stocks_data["tickers"][i] for i in ticker_indices
                ]
                self.all_stocks_data["tickers"] = filtered_tickers

                # Update sectors and industries to match filtered tickers
                filtered_sectors = {
                    ticker: self.all_stocks_data["sectors"][ticker]
                    for ticker in filtered_tickers
                    if ticker in self.all_stocks_data["sectors"]
                }
                filtered_industries = {
                    ticker: self.all_stocks_data["industries"][ticker]
                    for ticker in filtered_tickers
                    if ticker in self.all_stocks_data["industries"]
                }

                self.all_stocks_data["sectors"] = filtered_sectors
                self.all_stocks_data["industries"] = filtered_industries
                self.all_stocks_data["price"] = filtered_final_prices

                # Recalculate info with filtered data
                info = extract_hierarchical_info(filtered_sectors, filtered_industries)

                # Update counts and recalculate logp
                num_stocks = len(filtered_tickers)
                final_prices = filtered_final_prices
                logp = np.log(final_prices)
                t = logp.shape[1]  # Update time dimension

                logger.info(
                    f"Filtered dataset: {len(filtered_tickers)} stocks with complete information"
                )
                logger.info(
                    f"Final consistency check: price={final_prices.shape[0]}, hierarchical={info['num_stocks']}"
                )
                logger.info(f"Updated logp shape: {logp.shape}")
                logger.info(
                    f"Debug: valid_tickers={len(valid_tickers)}, filtered_tickers={len(filtered_tickers)}"
                )
                logger.info(
                    f"Debug: filtered_sectors={len(filtered_sectors)}, filtered_industries={len(filtered_industries)}"
                )

                # Verify final consistency
                if logp.shape[0] != info["num_stocks"]:
                    logger.error(
                        "CRITICAL: Still have dimension mismatch after filtering!"
                    )
                    logger.error(
                        f"logp shape: {logp.shape}, info stocks: {info['num_stocks']}"
                    )
                    logger.error("This indicates a bug in the filtering logic")
                    logger.error(
                        f"Debug: valid_tickers={len(valid_tickers)}, filtered_tickers={len(filtered_tickers)}"
                    )
                    logger.error(
                        f"Debug: filtered_sectors={len(filtered_sectors)}, filtered_industries={len(filtered_industries)}"
                    )
                    return False

                logger.info("✅ Data filtering successful - all dimensions match")

            # Prepare time features
            order = min(52, t // 4)  # Adaptive order based on data length
            info["tt"] = (
                np.linspace(1 / t, 1, t) ** np.arange(order + 1).reshape(-1, 1)
            ).astype("float32")
            info["order_scale"] = np.ones((1, order + 1), dtype="float32")

            # Train the model
            logger.info("Training MSIS-MCS model...")
            logger.info(
                f"Final training data: logp shape={logp.shape}, info stocks={info['num_stocks']}"
            )
            phi_m, psi_m, phi_s, psi_s, phi_i, psi_i, phi, psi = train_msis_mcs(
                logp, info, num_steps=10000
            )

            # Generate predictions
            horizon = 5  # 5-day prediction horizon
            tt_pred = (
                (1 + (np.arange(1 + horizon) / t))
                ** np.arange(order + 1).reshape(-1, 1)
            ).astype("float32")

            # Get price predictions and statistics
            logp_pred, std_logp_pred = estimate_logprice_statistics(phi, psi, tt_pred)
            price_pred, std_price_pred = estimate_price_statistics(
                logp_pred, std_logp_pred
            )

            # Calculate current vs predicted scores
            current_logp = logp[:, -1]  # Latest log prices
            predicted_logp = logp_pred[:, horizon]  # 5-day ahead predictions

            # Calculate trend scores (positive = above trend, negative = below trend)
            # Use the standard deviation for the same horizon as the prediction
            std_at_horizon = (
                std_logp_pred[:, horizon]
                if std_logp_pred.ndim > 1
                else std_logp_pred.squeeze()
            )
            trend_scores = (predicted_logp - current_logp) / std_at_horizon

            # Rate stocks based on trend with configurable extreme standards
            # Positive scores = above trend (overvalued), Negative scores = below trend (undervalued)
            # Analyze trend score distribution first
            self._analyze_trend_score_distribution(trend_scores)

            # 保存当前趋势分数用于动态阈值计算
            self.current_trend_scores = trend_scores

            # Use configurable thresholds to find extreme stocks (preserving original design intent)
            extreme_bounds = self._get_rating_bounds(self.extreme_level)
            ratings = self._rate_stocks_corrected(trend_scores, extreme_bounds)

            logger.info(
                f"Using '{self.extreme_level}' extreme level with bounds: {extreme_bounds}"
            )

            # Generate ML correlation matches for options trading
            logger.info("🔍 Generating ML correlation matches for options trading...")
            try:
                from core.stats import estimate_matches

                matches = estimate_matches(
                    self.all_stocks_data["tickers"], phi.numpy(), info["tt"]
                )
                logger.info(f"✅ Generated {len(matches)} ML correlation matches")
            except Exception as e:
                logger.warning(f"Failed to generate ML matches: {e}")
                matches = {}

            # Store model results
            self.model_results = {
                "tickers": self.all_stocks_data["tickers"],
                "current_prices": np.exp(current_logp),
                "predicted_prices": np.exp(predicted_logp),
                "trend_scores": trend_scores,
                "ratings": ratings,
                "sectors": self.all_stocks_data["sectors"],
                "industries": self.all_stocks_data["industries"],
                "matches": matches,  # Add ML correlation matches for options trading
                "price_data": {  # Add price data for options analysis
                    ticker: self.all_stocks_data["price"][i]
                    for i, ticker in enumerate(self.all_stocks_data["tickers"])
                },
                "model_params": {
                    "phi": phi.numpy(),
                    "psi": psi.numpy(),
                    "phi_m": phi_m.numpy(),
                    "psi_m": psi_m.numpy(),
                    "phi_s": phi_s.numpy(),
                    "psi_s": psi_s.numpy(),
                    "phi_i": phi_i.numpy(),
                    "psi_i": psi_i.numpy(),
                },
            }

            # Cache the model results
            with open(self.model_cache_file, "wb") as f:
                pickle.dump(self.model_results, f)

            logger.info("Model analysis completed successfully")
            logger.info(
                f"Stocks above trend: {sum(1 for r in ratings if 'ABOVE' in r)}"
            )
            logger.info(
                f"Stocks below trend: {sum(1 for r in ratings if 'BELOW' in r)}"
            )
            logger.info(
                f"Stocks along trend: {sum(1 for r in ratings if 'ALONG' in r)}"
            )

            return True

        except Exception as e:
            logger.error(f"Error in step 2: {e}")
            import traceback

            traceback.print_exc()
            return False

    def _rate_stocks_corrected(self, scores: np.ndarray, bounds: dict) -> list:
        """7级评级函数，包含极端分类"""
        ratings = []
        for score in scores:
            if score < bounds["EXTREME BELOW TREND"]:
                ratings.append("EXTREME BELOW TREND")
            elif score < bounds["HIGHLY BELOW TREND"]:
                ratings.append("HIGHLY BELOW TREND")
            elif score < bounds["BELOW TREND"]:
                ratings.append("BELOW TREND")
            elif score <= bounds["ALONG TREND"]:
                ratings.append("ALONG TREND")
            elif score <= bounds["ABOVE TREND"]:
                ratings.append("ABOVE TREND")
            elif score <= bounds["HIGHLY ABOVE TREND"]:
                ratings.append("HIGHLY ABOVE TREND")
            else:
                ratings.append("EXTREME ABOVE TREND")
        return ratings

    def _analyze_trend_score_distribution(self, trend_scores: np.ndarray):
        """分析趋势分数的分布以优化边界"""
        import numpy as np

        # Calculate percentiles
        percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        values = np.percentile(trend_scores, percentiles)

        logger.info("📊 Trend Score Distribution Analysis:")
        logger.info(
            f"  Min: {np.min(trend_scores):.3f}, Max: {np.max(trend_scores):.3f}"
        )
        logger.info(
            f"  Mean: {np.mean(trend_scores):.3f}, Std: {np.std(trend_scores):.3f}"
        )

        for p, v in zip(percentiles, values):
            logger.info(f"  {p:2d}th percentile: {v:6.3f}")

        # Count stocks in different ranges
        ranges = [
            ("< -5.0", np.sum(trend_scores < -5.0)),
            ("-5.0 to -4.0", np.sum((trend_scores >= -5.0) & (trend_scores < -4.0))),
            ("-4.0 to -3.0", np.sum((trend_scores >= -4.0) & (trend_scores < -3.0))),
            ("-3.0 to -2.5", np.sum((trend_scores >= -3.0) & (trend_scores < -2.5))),
            ("-2.5 to -2.0", np.sum((trend_scores >= -2.5) & (trend_scores < -2.0))),
            ("-2.0 to -1.0", np.sum((trend_scores >= -2.0) & (trend_scores < -1.0))),
            ("-1.0 to 0.0", np.sum((trend_scores >= -1.0) & (trend_scores < 0.0))),
            ("0.0 to 1.0", np.sum((trend_scores >= 0.0) & (trend_scores < 1.0))),
            ("1.0 to 2.0", np.sum((trend_scores >= 1.0) & (trend_scores < 2.0))),
            ("2.0 to 2.5", np.sum((trend_scores >= 2.0) & (trend_scores < 2.5))),
            ("2.5 to 3.0", np.sum((trend_scores >= 2.5) & (trend_scores < 3.0))),
            ("3.0 to 4.0", np.sum((trend_scores >= 3.0) & (trend_scores < 4.0))),
            ("4.0 to 5.0", np.sum((trend_scores >= 4.0) & (trend_scores < 5.0))),
            ("> 5.0", np.sum(trend_scores >= 5.0)),
        ]

        logger.info("📈 Stock Count by Range:")
        for range_name, count in ranges:
            percentage = (count / len(trend_scores)) * 100
            logger.info(f"  {range_name:12s}: {count:4d} stocks ({percentage:5.1f}%)")

    def _get_rating_bounds(self, extreme_level: str = "high") -> dict:
        """基于实际数据分布获取评级边界 - 动态校准"""
        # 动态计算阈值，基于当前数据的实际分布
        if hasattr(self, "current_trend_scores") and len(self.current_trend_scores) > 0:
            scores = np.array(self.current_trend_scores)
            p1 = np.percentile(scores, 1)  # 1st percentile
            p5 = np.percentile(scores, 5)  # 5th percentile
            p10 = np.percentile(scores, 10)  # 10th percentile
            p90 = np.percentile(scores, 90)  # 90th percentile
            p95 = np.percentile(scores, 95)  # 95th percentile
            p99 = np.percentile(scores, 99)  # 99th percentile

            logger.info("📊 Dynamic thresholds based on current data:")
            logger.info(f"   1st percentile: {p1:.1f}, 5th: {p5:.1f}, 10th: {p10:.1f}")
            logger.info(
                f"   90th percentile: {p90:.1f}, 95th: {p95:.1f}, 99th: {p99:.1f}"
            )
        else:
            # 回退到静态阈值
            p1, p5, p10 = -23.0, -10.0, -5.0
            p90, p95, p99 = 29.0, 37.0, 42.0

        if extreme_level == "high":
            # High extreme standards - truly ultra-rare boundaries
            return {
                "EXTREME BELOW TREND": p1 - 5.0,  # 比1st percentile更低
                "HIGHLY BELOW TREND": p1,  # 1st percentile
                "BELOW TREND": p5,  # 5th percentile
                "ALONG TREND": p90,  # Normal range (5th to 90th)
                "ABOVE TREND": p95,  # 95th percentile
                "HIGHLY ABOVE TREND": p99,  # 99th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >99th percentile
            }
        elif extreme_level == "medium":
            # Medium extreme standards - rare but accessible
            return {
                "EXTREME BELOW TREND": p5,  # 5th percentile
                "HIGHLY BELOW TREND": p10,  # 10th percentile
                "BELOW TREND": p10 + 2.0,  # 略高于10th percentile
                "ALONG TREND": p90,  # Normal range
                "ABOVE TREND": p90 + 2.0,  # 略高于90th percentile
                "HIGHLY ABOVE TREND": p95,  # 95th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >95th percentile
            }
        else:  # "low"
            # Low extreme standards - more opportunities
            return {
                "EXTREME BELOW TREND": -23.0,  # ~1st percentile
                "HIGHLY BELOW TREND": -10.0,  # ~5th percentile
                "BELOW TREND": -3.0,  # ~12th percentile
                "ALONG TREND": 25.0,  # Normal range (-3 to 25)
                "ABOVE TREND": 32.0,  # ~95th percentile
                "HIGHLY ABOVE TREND": 37.0,  # ~99th percentile
                "EXTREME ABOVE TREND": float("inf"),  # >99th percentile
            }

    def step3_correlation_analysis(self) -> bool:
        """步骤3: 执行相关性分析以识别交易机会"""
        logger.info("Step 3: Performing correlation analysis...")

        try:
            if not self.model_results:
                logger.error("No model results available for correlation analysis")
                return False

            # Get stocks that are significantly above or below trend
            tickers = self.model_results["tickers"]
            ratings = self.model_results["ratings"]
            trend_scores = self.model_results["trend_scores"]
            sectors = self.model_results["sectors"]

            # Identify candidate stocks across all 7 trend categories
            extreme_below_trend = []
            highly_below_trend = []
            below_trend = []
            along_trend = []
            above_trend = []
            highly_above_trend = []
            extreme_above_trend = []

            for i, (ticker, rating, score) in enumerate(
                zip(tickers, ratings, trend_scores)
            ):
                stock_info = {
                    "ticker": ticker,
                    "score": score,
                    "sector": sectors[ticker],
                    "current_price": self.model_results["current_prices"][i],
                    "predicted_price": self.model_results["predicted_prices"][i],
                }

                if "EXTREME BELOW" in rating:
                    extreme_below_trend.append(stock_info)
                elif "HIGHLY BELOW" in rating:
                    highly_below_trend.append(stock_info)
                elif (
                    "BELOW" in rating
                    and "HIGHLY" not in rating
                    and "EXTREME" not in rating
                ):
                    below_trend.append(stock_info)
                elif "ALONG" in rating:
                    along_trend.append(stock_info)
                elif (
                    "ABOVE" in rating
                    and "HIGHLY" not in rating
                    and "EXTREME" not in rating
                ):
                    above_trend.append(stock_info)
                elif "HIGHLY ABOVE" in rating:
                    highly_above_trend.append(stock_info)
                elif "EXTREME ABOVE" in rating:
                    extreme_above_trend.append(stock_info)

            # Perform correlation analysis within sectors
            correlation_opportunities = []

            # Group by sector for correlation analysis (include extreme categories)
            sector_groups = {}
            for stock in (
                extreme_below_trend
                + highly_below_trend
                + highly_above_trend
                + extreme_above_trend
            ):
                sector = stock["sector"]
                if sector not in sector_groups:
                    sector_groups[sector] = []
                sector_groups[sector].append(stock)

            # Find correlation opportunities within each sector
            for sector, stocks in sector_groups.items():
                if len(stocks) >= 2:
                    # Sort by trend score
                    stocks.sort(key=lambda x: x["score"])

                    # Look for pairs with opposite trends for potential correlation trades
                    sector_below_trend = [
                        s for s in stocks if s["score"] < -1.0
                    ]  # Undervalued (buy candidates)
                    sector_above_trend = [
                        s for s in stocks if s["score"] > 1.0
                    ]  # Overvalued (sell/avoid candidates)

                    if sector_below_trend and sector_above_trend:
                        correlation_opportunities.append(
                            {
                                "sector": sector,
                                "buy_candidates": sector_below_trend,  # Buy undervalued
                                "sell_candidates": sector_above_trend,  # Sell overvalued
                                "correlation_strength": len(sector_below_trend)
                                + len(sector_above_trend),
                            }
                        )

            self.correlation_analysis = {
                "extreme_below_trend": extreme_below_trend,
                "highly_below_trend": highly_below_trend,
                "below_trend": below_trend,
                "along_trend": along_trend,
                "above_trend": above_trend,
                "highly_above_trend": highly_above_trend,
                "extreme_above_trend": extreme_above_trend,
                "correlation_opportunities": correlation_opportunities,
                "sector_groups": sector_groups,
            }

            logger.info(f"Found {len(extreme_below_trend)} stocks EXTREME BELOW trend")
            logger.info(f"Found {len(highly_below_trend)} stocks HIGHLY BELOW trend")
            logger.info(f"Found {len(below_trend)} stocks BELOW trend")
            logger.info(f"Found {len(along_trend)} stocks ALONG trend")
            logger.info(f"Found {len(above_trend)} stocks ABOVE trend")
            logger.info(f"Found {len(highly_above_trend)} stocks HIGHLY ABOVE trend")
            logger.info(f"Found {len(extreme_above_trend)} stocks EXTREME ABOVE trend")
            logger.info(
                f"Identified {len(correlation_opportunities)} correlation opportunities"
            )

            return True

        except Exception as e:
            logger.error(f"Error in step 3: {e}")
            return False

    async def step4_execute_trades(self) -> bool:
        """步骤4: 基于相关性分析执行交易"""
        logger.info("Step 4: Executing trades based on correlation analysis...")

        try:
            if not self.correlation_analysis:
                logger.error("No correlation analysis available for trading")
                return False

            # Check if we already have an IBKR connection from data download
            if not self.ibkr_client.connected:
                logger.info(
                    "No existing IBKR connection - establishing new connection for trading"
                )
                if not await self.ibkr_client.connect():
                    logger.error("Failed to connect to IBKR for trading")
                    return False
            else:
                logger.info("✅ Using existing IBKR connection for trading")

            # Prepare trading info for the bot based on bot's preference
            trading_info = {}

            # Determine bot's trading preference
            bot_name = self.bot.__class__.__name__

            if bot_name in ["Betty", "Eddy"]:
                # Aggressive bots - buy strong/above trend stocks, prioritize EXTREME opportunities
                logger.info(
                    f"{bot_name} bot prefers strong stocks - prioritizing EXTREME ABOVE TREND candidates"
                )

                # First priority: EXTREME ABOVE TREND stocks (ultra rare opportunities)
                extreme_candidates = sorted(
                    self.correlation_analysis.get("extreme_above_trend", []),
                    key=lambda x: x["score"],
                    reverse=True,  # Highest scores first
                )[:3]  # Only top 3 extreme opportunities

                # Second priority: HIGHLY ABOVE TREND stocks
                highly_candidates = sorted(
                    self.correlation_analysis.get("highly_above_trend", []),
                    key=lambda x: x["score"],
                    reverse=True,
                )[:7]  # Up to 7 highly above trend

                # Combine candidates
                buy_candidates = extreme_candidates + highly_candidates

                for stock in buy_candidates:
                    if "EXTREME" in self._get_stock_rating_from_score(stock["score"]):
                        rate = "EXTREME ABOVE TREND"
                        logger.info(
                            f"🔥 ULTRA RARE: {stock['ticker']} (score: {stock['score']:.2f})"
                        )
                    else:
                        rate = "HIGHLY ABOVE TREND"

                    trading_info[stock["ticker"]] = {
                        "price": stock["current_price"],
                        "rate": rate,
                        "growth": (stock["predicted_price"] - stock["current_price"])
                        / stock["current_price"],
                        "score": stock["score"],
                        "action": "BUY",
                    }

                # Add regular above trend if not enough
                if len(buy_candidates) < 5:
                    regular_above = [
                        s
                        for s in self.correlation_analysis.get("above_trend", [])
                        if s["ticker"] not in trading_info
                    ][: 5 - len(buy_candidates)]
                    for stock in regular_above:
                        trading_info[stock["ticker"]] = {
                            "price": stock["current_price"],
                            "rate": "ABOVE TREND",
                            "growth": (
                                stock["predicted_price"] - stock["current_price"]
                            )
                            / stock["current_price"],
                            "score": stock["score"],
                            "action": "BUY",
                        }

            elif bot_name == "Flora":
                # Flora prefers along trend stocks with high growth
                logger.info(
                    f"{bot_name} bot prefers stable growth stocks - selecting ALONG TREND candidates"
                )

                along_trend_stocks = self.correlation_analysis.get("along_trend", [])
                # Filter for high growth stocks
                high_growth_stocks = [
                    s
                    for s in along_trend_stocks
                    if (s["predicted_price"] - s["current_price"]) / s["current_price"]
                    >= 1.0
                ]

                buy_candidates = sorted(
                    high_growth_stocks,
                    key=lambda x: (x["predicted_price"] - x["current_price"])
                    / x["current_price"],
                    reverse=True,
                )[:10]

                for stock in buy_candidates:
                    trading_info[stock["ticker"]] = {
                        "price": stock["current_price"],
                        "rate": "ALONG TREND",
                        "growth": (stock["predicted_price"] - stock["current_price"])
                        / stock["current_price"],
                        "score": stock["score"],
                        "action": "BUY",
                    }

            elif bot_name == "Chris":
                # Chris only buys specific stocks
                logger.info(
                    f"{bot_name} bot has specific stock preferences - checking buy_only list"
                )

                # Check if any of Chris's preferred stocks are available
                chris_stocks = ["GOOGL", "AMZN", "AAPL", "MSFT", "META"]
                all_stocks = (
                    self.correlation_analysis.get("extreme_below_trend", [])
                    + self.correlation_analysis.get("highly_below_trend", [])
                    + self.correlation_analysis.get("below_trend", [])
                    + self.correlation_analysis.get("along_trend", [])
                    + self.correlation_analysis.get("above_trend", [])
                    + self.correlation_analysis.get("highly_above_trend", [])
                    + self.correlation_analysis.get("extreme_above_trend", [])
                )

                for stock in all_stocks:
                    if stock["ticker"] in chris_stocks:
                        trading_info[stock["ticker"]] = {
                            "price": stock["current_price"],
                            "rate": self._get_stock_rating(stock["ticker"]),
                            "growth": (
                                stock["predicted_price"] - stock["current_price"]
                            )
                            / stock["current_price"],
                            "score": stock["score"],
                            "action": "BUY",
                        }

            else:
                # Conservative bots (Adam, Dany) - buy undervalued/below trend stocks, prioritize EXTREME opportunities
                logger.info(
                    f"{bot_name} bot prefers undervalued stocks - prioritizing EXTREME BELOW TREND candidates"
                )

                # First priority: EXTREME BELOW TREND stocks (ultra rare value opportunities)
                extreme_candidates = sorted(
                    self.correlation_analysis.get("extreme_below_trend", []),
                    key=lambda x: x["score"],
                )[:3]  # Only top 3 extreme value opportunities (lowest scores first)

                # Second priority: HIGHLY BELOW TREND stocks
                highly_candidates = sorted(
                    self.correlation_analysis.get("highly_below_trend", []),
                    key=lambda x: x["score"],
                )[:7]  # Up to 7 highly undervalued

                # Combine candidates
                buy_candidates = extreme_candidates + highly_candidates

                for stock in buy_candidates:
                    if "EXTREME" in self._get_stock_rating_from_score(stock["score"]):
                        rate = "EXTREME BELOW TREND"
                        logger.info(
                            f"💎 ULTRA VALUE: {stock['ticker']} (score: {stock['score']:.2f})"
                        )
                    else:
                        rate = "HIGHLY BELOW TREND"

                    trading_info[stock["ticker"]] = {
                        "price": stock["current_price"],
                        "rate": rate,
                        "growth": (stock["predicted_price"] - stock["current_price"])
                        / stock["current_price"],
                        "score": stock["score"],
                        "action": "BUY",  # Explicit buy signal
                    }

                # Add some regular below trend stocks if not enough
                if len(buy_candidates) < 5:
                    regular_below = [
                        s
                        for s in self.correlation_analysis.get("below_trend", [])
                        if s["ticker"] not in trading_info
                    ][: 5 - len(buy_candidates)]
                    for stock in regular_below:
                        trading_info[stock["ticker"]] = {
                            "price": stock["current_price"],
                            "rate": "BELOW TREND",
                            "growth": (
                                stock["predicted_price"] - stock["current_price"]
                            )
                            / stock["current_price"],
                            "score": stock["score"],
                            "action": "BUY",
                        }

            if not trading_info:
                logger.info(f"No trading opportunities identified for {bot_name} bot")
                logger.info("This could be due to:")
                logger.info("- No stocks matching bot's preference")
                logger.info("- Market conditions not suitable for bot's strategy")
                logger.info("Portfolio will remain unchanged")
                return True

            logger.info(f"Executing trades for {len(trading_info)} stocks...")

            # Execute trading strategy using the bot
            self.bot.trade(trading_info)

            # Execute any queued real trades
            if hasattr(self.bot, "execute_queued_trades"):
                # Ensure bot is connected to IBKR before executing real trades
                if hasattr(self.bot, "connect_to_ibkr"):
                    connected = await self.bot.connect_to_ibkr()
                    if connected:
                        logger.info("✅ Bot connected to IBKR for trade execution")
                        await self.bot.execute_queued_trades()
                    else:
                        logger.warning(
                            "❌ Failed to connect bot to IBKR - trades will remain in simulation mode"
                        )
                else:
                    await self.bot.execute_queued_trades()

            # Update portfolio value
            current_prices = {
                ticker: info["price"] for ticker, info in trading_info.items()
            }
            self.bot.compute_capital(current_prices)

            # Store trading decisions
            self.trading_decisions = {
                "timestamp": datetime.now(),
                "trading_info": trading_info,
                "portfolio_value": self.bot.capital,
                "cash": self.bot.uninvested,
                "invested": self.bot.invested,
                "positions": dict(self.bot.portfolio),
            }

            logger.info("Trading completed:")
            logger.info(f"Portfolio value: ${self.bot.capital:.2f}")
            logger.info(f"Cash: ${self.bot.uninvested:.2f}")
            logger.info(f"Invested: ${self.bot.invested:.2f}")
            logger.info(f"Positions: {len(self.bot.portfolio)}")

            return True

        except Exception as e:
            logger.error(f"Error in step 4: {e}")
            return False
        finally:
            # Disconnect if connected
            if self.ibkr_client.connected:
                self.ibkr_client.disconnect()

            # Also disconnect bot's IBKR client if connected
            if hasattr(self.bot, "disconnect_from_ibkr"):
                self.bot.disconnect_from_ibkr()

    async def step5_options_arbitrage(self) -> bool:
        """步骤5: 基于相关性背离执行期权套利"""
        logger.info("🚀 Step 5: Analyzing options arbitrage opportunities...")
        logger.info(
            f"📊 Available data: model_results={bool(self.model_results)}, correlation_analysis={bool(self.correlation_analysis)}"
        )

        try:
            if not self.model_results or not self.correlation_analysis:
                logger.error(
                    "❌ No model results or correlation analysis available for options arbitrage"
                )
                return False

            # Log correlation analysis structure
            if self.correlation_analysis:
                logger.info(
                    f"📈 Correlation analysis categories: {list(self.correlation_analysis.keys())}"
                )
                for category, stocks in self.correlation_analysis.items():
                    if isinstance(stocks, list):
                        logger.info(f"   {category}: {len(stocks)} stocks")

            # Import options arbitrage system
            logger.info("🔧 Importing options arbitrage system...")
            from options.arbitrage import OptionsArbitrageSystem

            # Initialize options arbitrage system
            logger.info("⚙️ Initializing options arbitrage system...")
            options_system = OptionsArbitrageSystem(
                self.config, self.options_charts_dir
            )

            # Calculate capital allocation - 增加期权资金分配以支持交易
            options_capital = self.bot.capital * 0.3  # Use 30% of capital for options
            logger.info(
                f"💰 Options capital allocation: ${options_capital:,.2f} (30% of ${self.bot.capital:,.2f})"
            )

            # Run options arbitrage analysis
            logger.info("🎯 Starting options arbitrage analysis...")
            success = await options_system.run_options_arbitrage(
                self.correlation_analysis, self.model_results, options_capital
            )

            if success:
                logger.info("✅ Options arbitrage analysis completed successfully")
            else:
                logger.warning("⚠️ Options arbitrage analysis completed with issues")

            return success

        except Exception as e:
            logger.error(f"❌ Error in step 5 (options arbitrage): {e}")
            import traceback

            logger.error(f"📋 Traceback: {traceback.format_exc()}")
            return False

    async def step6_manage_portfolio_risk(self) -> bool:
        """步骤6: 使用智能止损管理投资组合风险"""
        logger.info("Step 6: Managing portfolio risk with smart stop losses...")

        try:
            if not self.model_results or not self.correlation_analysis:
                logger.error(
                    "No model results or correlation analysis available for risk management"
                )
                return False

            # Initialize portfolio manager if not already done
            if not self.portfolio_manager:
                self.portfolio_manager = PortfolioManager(self.bot, self.config)

            # Prepare market data for risk management
            market_data = self._prepare_market_data_for_risk_management()

            if not market_data:
                logger.info("No market data available for risk management")
                return True

            # Execute risk management cycle
            risk_report = await self.portfolio_manager.execute_risk_management_cycle(
                market_data, self.correlation_analysis
            )

            if risk_report.get("status") == "success":
                logger.info("✅ Portfolio risk management completed successfully")

                # Log key metrics
                metrics = risk_report.get("portfolio_metrics", {})
                if metrics:
                    logger.info(
                        f"📊 Portfolio Value: ${metrics.get('total_value', 0):,.2f}"
                    )
                    logger.info(
                        f"💰 Total P&L: ${metrics.get('total_pnl', 0):,.2f} ({metrics.get('total_pnl_pct', 0):.1f}%)"
                    )
                    logger.info(
                        f"🛡️ Risk Level: {risk_report.get('risk_level', 'Unknown')}"
                    )
                    logger.info(
                        f"📋 Stop Loss Orders: {risk_report.get('stop_loss_orders', 0)}"
                    )

                # Log recommendations
                recommendations = risk_report.get("recommendations", [])
                if recommendations:
                    logger.info("💡 Risk Management Recommendations:")
                    for rec in recommendations:
                        logger.info(f"  {rec}")

                return True
            else:
                logger.warning(
                    f"Risk management completed with status: {risk_report.get('status')}"
                )
                return True

        except Exception as e:
            logger.error(f"Error in step 5: {e}")
            return False

    def _prepare_market_data_for_risk_management(self) -> Dict:
        """准备投资组合管理器期望格式的市场数据"""
        if not self.model_results:
            return {}

        market_data = {}

        for i, ticker in enumerate(self.model_results["tickers"]):
            market_data[ticker] = {
                "current_price": self.model_results["current_prices"][i],
                "predicted_price": self.model_results["predicted_prices"][i],
                "trend_score": self.model_results["trend_scores"][i],
                "sector": self.model_results["sectors"].get(ticker, "Unknown"),
                "industry": self.model_results["industries"].get(ticker, "Unknown"),
                "volatility": self._estimate_volatility(ticker, i),
            }

        return market_data

    def _get_stock_rating(self, ticker: str) -> str:
        """获取特定股票代码的评级"""
        # Search through all 7 rating categories
        for rating, stocks in [
            (
                "EXTREME BELOW TREND",
                self.correlation_analysis.get("extreme_below_trend", []),
            ),
            (
                "HIGHLY BELOW TREND",
                self.correlation_analysis.get("highly_below_trend", []),
            ),
            ("BELOW TREND", self.correlation_analysis.get("below_trend", [])),
            ("ALONG TREND", self.correlation_analysis.get("along_trend", [])),
            ("ABOVE TREND", self.correlation_analysis.get("above_trend", [])),
            (
                "HIGHLY ABOVE TREND",
                self.correlation_analysis.get("highly_above_trend", []),
            ),
            (
                "EXTREME ABOVE TREND",
                self.correlation_analysis.get("extreme_above_trend", []),
            ),
        ]:
            for stock in stocks:
                if stock["ticker"] == ticker:
                    return rating
        return "UNKNOWN"

    def _get_stock_rating_from_score(self, score: float) -> str:
        """使用当前边界基于趋势分数获取评级"""
        bounds = self._get_rating_bounds(self.extreme_level)

        if score < bounds["EXTREME BELOW TREND"]:
            return "EXTREME BELOW TREND"
        elif score < bounds["HIGHLY BELOW TREND"]:
            return "HIGHLY BELOW TREND"
        elif score < bounds["BELOW TREND"]:
            return "BELOW TREND"
        elif score <= bounds["ALONG TREND"]:
            return "ALONG TREND"
        elif score <= bounds["ABOVE TREND"]:
            return "ABOVE TREND"
        elif score <= bounds["HIGHLY ABOVE TREND"]:
            return "HIGHLY ABOVE TREND"
        else:
            return "EXTREME ABOVE TREND"

    def _estimate_volatility(self, ticker: str, index: int) -> float:
        """估算股票波动性 (简化计算)"""
        try:
            if self.all_stocks_data and "price" in self.all_stocks_data:
                prices = self.all_stocks_data["price"][index]
                if len(prices) > 20:
                    # Calculate 20-day volatility
                    returns = np.diff(np.log(prices[-20:]))
                    volatility = np.std(returns) * np.sqrt(252)  # Annualized
                    return float(volatility)
        except Exception:
            pass

        # Default volatility if calculation fails
        return 0.25

    def _load_stock_info_from_csv(self):
        """从stock_info.csv加载股票分类信息"""
        stock_info = {}
        try:
            import pandas as pd

            if os.path.exists("data/stock_info/stock_info.csv"):
                df = pd.read_csv("data/stock_info/stock_info.csv")
                for _, row in df.iterrows():
                    symbol = row["symbol"]
                    stock_info[symbol] = {
                        "sector": row.get("sector", "Unknown"),
                        "industry": row.get("industry", "Unknown"),
                        "subcategory": row.get("subcategory", "Unknown"),
                        "long_name": row.get("long_name", symbol),
                    }
                logger.info(
                    f"Loaded classification info for {len(stock_info)} stocks from stock_info.csv"
                )
            else:
                logger.warning(
                    "data/stock_info/stock_info.csv not found, using default classifications"
                )
        except Exception as e:
            logger.warning(f"Failed to load data/stock_info/stock_info.csv: {e}")
        return stock_info

    def save_daily_results(self):
        """将日常结果保存到CSV文件"""
        try:
            if not self.model_results:
                return

            # Ensure results directory exists
            results_dir = os.path.dirname(self.results_file)
            if not os.path.exists(results_dir):
                os.makedirs(results_dir)
                logger.info(f"📁 Created results directory: {results_dir}")

            # Load stock classification info from CSV
            stock_info = self._load_stock_info_from_csv()

            # Prepare results DataFrame
            results_data = []

            for i, ticker in enumerate(self.model_results["tickers"]):
                # Get classification from CSV if available, otherwise use model data
                if ticker in stock_info:
                    sector = stock_info[ticker]["sector"]
                    industry = stock_info[ticker]["industry"]
                else:
                    sector = self.model_results["sectors"].get(ticker, "Unknown")
                    industry = self.model_results["industries"].get(ticker, "Unknown")

                results_data.append(
                    {
                        "date": datetime.now().strftime("%Y-%m-%d"),
                        "ticker": ticker,
                        "current_price": self.model_results["current_prices"][i],
                        "predicted_price": self.model_results["predicted_prices"][i],
                        "trend_score": self.model_results["trend_scores"][i],
                        "rating": self.model_results["ratings"][i],
                        "sector": sector,
                        "industry": industry,
                    }
                )

            df = pd.DataFrame(results_data)
            df.to_csv(self.results_file, index=False)

            logger.info(f"📊 Daily results saved to {self.results_file}")

        except Exception as e:
            logger.error(f"Error saving daily results: {e}")

    async def run_daily_analysis(self) -> bool:
        """运行完整的日常分析工作流"""
        logger.info("🚀 Starting daily trading analysis workflow...")
        logger.info(
            f"🔧 System configuration: enable_options={getattr(self, 'enable_options', False)}"
        )
        logger.info("=" * 60)

        # Step 0: Update stock universe (optional)
        if not await self.step0_update_stock_universe():
            logger.error("Step 0 failed - aborting workflow")
            return False

        # Step 1: Download all stock data
        if not await self.step1_download_all_stocks():
            logger.error("Step 1 failed - aborting workflow")
            return False

        # Step 2: Run model analysis
        if not self.step2_run_model_analysis():
            logger.error("Step 2 failed - aborting workflow")
            return False

        # Step 3: Correlation analysis
        if not self.step3_correlation_analysis():
            logger.error("Step 3 failed - aborting workflow")
            return False

        # Step 4: Execute trades
        if not await self.step4_execute_trades():
            logger.error("Step 4 failed - aborting workflow")
            return False

        # Step 5: Options arbitrage (if enabled)
        logger.info(
            f"🔍 Checking options arbitrage: enable_options={getattr(self, 'enable_options', False)}"
        )
        if hasattr(self, "enable_options") and self.enable_options:
            logger.info("✅ Options arbitrage is ENABLED - starting Step 5")
            if not await self.step5_options_arbitrage():
                logger.warning("Step 5 (options arbitrage) failed - continuing")
        else:
            logger.info("❌ Options arbitrage is DISABLED - skipping Step 5")

        # Step 6: Manage portfolio risk with smart stop losses
        if not await self.step6_manage_portfolio_risk():
            logger.warning("Step 6 failed - continuing without risk management")
            # Don't abort workflow for risk management failure

        # Save results
        self.save_daily_results()

        logger.info("=" * 60)
        logger.info("Daily trading analysis completed successfully!")

        return True


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Daily Trading System")
    parser.add_argument(
        "--bot",
        choices=["Adam", "Betty", "Chris", "Dany", "Eddy", "Flora"],
        default="Adam",
        help="Trading bot to use",
    )
    parser.add_argument(
        "--capital", type=float, default=10000.0, help="Initial capital"
    )
    parser.add_argument("--paper", action="store_true", help="Use paper trading")
    parser.add_argument(
        "--live", action="store_true", help="Use live trading (DANGEROUS!)"
    )
    parser.add_argument(
        "--update-universe",
        action="store_true",
        help="Update stock universe before analysis (removes invalid symbols)",
    )
    parser.add_argument(
        "--extreme-level",
        type=str,
        default="high",
        choices=["high", "medium", "low"],
        help="Extreme threshold level: high (1-5%%), medium (5-15%%), low (10-30%%)",
    )
    parser.add_argument(
        "--clean-cache",
        action="store_true",
        help="Clean cache files for fresh download (fixes slow concurrent downloads)",
    )
    parser.add_argument(
        "--resume",
        action="store_true",
        help="Resume interrupted download from last checkpoint",
    )
    parser.add_argument(
        "--skip-download",
        action="store_true",
        help="Skip data download and use existing cached data for testing",
    )
    parser.add_argument(
        "--enable-options",
        action="store_true",
        help="Enable options arbitrage trading based on correlation divergence",
    )

    args = parser.parse_args()

    # Select bot class
    bot_classes = {
        "Adam": Adam,
        "Betty": Betty,
        "Chris": Chris,
        "Dany": Dany,
        "Eddy": Eddy,
        "Flora": Flora,
    }
    bot_class = bot_classes[args.bot]

    # Configure trading mode
    config = IBKRConfig()

    if args.live:
        # Live trading mode - uses real money
        config.port = 7496  # Live trading port
        print("⚠️  LIVE TRADING MODE - REAL MONEY AT RISK!")
        print("⚠️  This will execute real trades with your actual funds!")
        print("⚠️  Make sure you understand the risks before proceeding!")
        confirm = input("Type 'YES' to confirm live trading: ").strip()
        if confirm != "YES":
            print(f"Live trading cancelled (you entered: '{confirm}')")
            print("Note: You must type exactly 'YES' (all caps) to confirm")
            return
        print("🔴 LIVE TRADING MODE CONFIRMED - USING REAL MONEY!")

    elif args.paper:
        # Paper trading mode - uses IBKR paper trading
        config.port = 7497  # Paper trading port
        print("📝 Paper trading mode (IBKR simulation)")

    else:
        # Default to paper trading for safety
        config.port = 7497  # Paper trading port
        print("📝 Default paper trading mode (use --paper or --live to specify)")
        print("💡 Use --paper for IBKR paper trading or --live for real trading")

    # Create and run daily trading system
    system = DailyTradingSystem(
        config,
        bot_class,
        args.capital,
        args.update_universe,
        args.extreme_level,
        args.skip_download,
        args.enable_options,
    )

    # Clean cache if requested (fixes slow concurrent downloads)
    if args.clean_cache:
        system.clean_cache_files()
        print("🚀 Cache cleaned - system will now use optimized sequential download")

    # Handle resume functionality
    if args.resume:
        print("🔄 Resume mode - will continue from last checkpoint")
        # Also clean invalid symbols if progress file exists
        system.clean_invalid_symbols()

    success = await system.run_daily_analysis()

    if success:
        print("✅ Daily analysis completed successfully!")
    else:
        print("❌ Daily analysis failed!")


if __name__ == "__main__":
    asyncio.run(main())
